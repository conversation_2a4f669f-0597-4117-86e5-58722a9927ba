#!/usr/bin/env python3
"""
Demo script to test CNN-SVM implementation
"""

import os
import numpy as np
import matplotlib.pyplot as plt
from cnn_svm_trainer import CNNSVMClassifier
from sklearn.model_selection import train_test_split
import time

def create_sample_data(num_samples=100, img_size=(224, 224)):
    """Create sample data for testing"""
    print("Creating sample data for testing...")
    
    # Generate random images
    X = np.random.rand(num_samples, img_size[0], img_size[1], 3).astype('float32')
    
    # Generate random binary labels
    y = np.random.choice(['Normal', 'Dyslexia'], size=num_samples)
    
    return X, y

def test_cnn_svm_pipeline():
    """Test the complete CNN-SVM pipeline with sample data"""
    print("="*60)
    print("TESTING CNN-SVM PIPELINE")
    print("="*60)
    
    # Create sample data
    X, y = create_sample_data(num_samples=200, img_size=(224, 224))
    
    print(f"Sample data created: {X.shape[0]} images")
    print(f"Image shape: {X.shape[1:]}")
    print(f"Classes: {np.unique(y)}")
    
    # Split data
    X_train, X_test, y_train, y_test = train_test_split(
        X, y, test_size=0.3, random_state=42, stratify=y
    )
    
    X_train, X_val, y_train, y_val = train_test_split(
        X_train, y_train, test_size=0.2, random_state=42, stratify=y_train
    )
    
    print(f"Training set: {X_train.shape[0]} images")
    print(f"Validation set: {X_val.shape[0]} images")
    print(f"Test set: {X_test.shape[0]} images")
    
    # Initialize classifier
    classifier = CNNSVMClassifier(
        img_size=(224, 224),
        use_pretrained=False,  # Use custom CNN for faster testing
        model_type='Custom'
    )
    
    # Train CNN feature extractor
    print("\nTraining CNN feature extractor...")
    start_time = time.time()
    history = classifier.build_and_train_cnn(
        X_train, y_train, X_val, y_val, epochs=3  # Few epochs for demo
    )
    cnn_time = time.time() - start_time
    print(f"CNN training completed in {cnn_time:.2f} seconds")
    
    # Train SVM classifier with smaller parameter grid
    print("\nTraining SVM classifier...")
    start_time = time.time()
    
    # Simplified parameter grid for demo
    param_grid = {
        'C': [1, 10],
        'kernel': ['rbf', 'linear'],
        'gamma': ['scale']
    }
    
    best_score = classifier.train_svm(X_train, y_train, param_grid)
    svm_time = time.time() - start_time
    print(f"SVM training completed in {svm_time:.2f} seconds")
    
    # Evaluate model
    print("\nEvaluating model on test set...")
    accuracy, report, cm = classifier.evaluate(X_test, y_test)
    
    # Test prediction on single sample
    print("\nTesting single prediction...")
    sample_image = X_test[:1]  # Take first test image
    predictions, probabilities = classifier.predict(sample_image)
    
    print(f"Sample prediction: {predictions[0]}")
    print(f"Prediction probabilities: {probabilities[0]}")
    
    # Save model
    print("\nSaving model...")
    classifier.save_model("demo_model")
    
    # Test loading model
    print("Testing model loading...")
    new_classifier = CNNSVMClassifier()
    new_classifier.load_model("demo_model")
    
    # Test prediction with loaded model
    new_predictions, new_probabilities = new_classifier.predict(sample_image)
    print(f"Loaded model prediction: {new_predictions[0]}")
    
    print("\n" + "="*60)
    print("DEMO COMPLETED SUCCESSFULLY!")
    print("="*60)
    print(f"Final test accuracy: {accuracy:.4f} ({accuracy*100:.2f}%)")
    print(f"Total training time: {cnn_time + svm_time:.2f} seconds")
    
    return classifier

def check_dependencies():
    """Check if all required dependencies are installed"""
    print("Checking dependencies...")
    
    required_packages = [
        'tensorflow', 'sklearn', 'cv2', 'numpy', 
        'pandas', 'matplotlib', 'seaborn', 'tqdm'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            if package == 'sklearn':
                import sklearn
            elif package == 'cv2':
                import cv2
            else:
                __import__(package)
            print(f"✓ {package}")
        except ImportError:
            print(f"✗ {package} - MISSING")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\nMissing packages: {missing_packages}")
        print("Please install them using: pip install -r requirements.txt")
        return False
    else:
        print("\nAll dependencies are installed!")
        return True

def check_dataset_structure():
    """Check if datasets are properly structured"""
    print("\nChecking dataset structure...")
    
    # Check Model 1
    model1_path = "Model 1 Normal and dyslexia"
    if os.path.exists(model1_path):
        print(f"✓ Found {model1_path}")
        
        # Check subdirectories
        train_path = os.path.join(model1_path, "Train")
        test_path = os.path.join(model1_path, "test")
        
        if os.path.exists(train_path):
            print(f"  ✓ Found Train directory")
            normal_path = os.path.join(train_path, "Normal")
            dyslexia_path = os.path.join(train_path, "Dyslexia")
            
            if os.path.exists(normal_path):
                print(f"    ✓ Found Normal directory")
            else:
                print(f"    ✗ Missing Normal directory")
                
            if os.path.exists(dyslexia_path):
                print(f"    ✓ Found Dyslexia directory")
            else:
                print(f"    ✗ Missing Dyslexia directory")
        else:
            print(f"  ✗ Missing Train directory")
            
        if os.path.exists(test_path):
            print(f"  ✓ Found test directory")
        else:
            print(f"  ✗ Missing test directory")
    else:
        print(f"✗ Dataset not found: {model1_path}")
    
    # Check Model 2
    model2_path = "Model 2 Alphabets 26 letters"
    if os.path.exists(model2_path):
        print(f"✓ Found {model2_path}")
        
        train_path = os.path.join(model2_path, "train")
        test_path = os.path.join(model2_path, "test")
        
        if os.path.exists(train_path):
            print(f"  ✓ Found train directory")
        else:
            print(f"  ✗ Missing train directory")
            
        if os.path.exists(test_path):
            print(f"  ✓ Found test directory")
        else:
            print(f"  ✗ Missing test directory")
    else:
        print(f"✗ Dataset not found: {model2_path}")

def main():
    """Main demo function"""
    print("CNN-SVM Handwriting Analysis - Demo")
    print("="*60)
    
    # Check dependencies
    if not check_dependencies():
        return
    
    # Check dataset structure
    check_dataset_structure()
    
    # Ask user if they want to run the demo
    print("\n" + "="*60)
    response = input("Do you want to run the demo with sample data? (y/n): ")
    
    if response.lower() in ['y', 'yes']:
        try:
            classifier = test_cnn_svm_pipeline()
            print("\nDemo completed successfully!")
            print("You can now train on your actual datasets using:")
            print("  python train_models.py --model both")
        except Exception as e:
            print(f"Demo failed with error: {e}")
            print("Please check your installation and try again.")
    else:
        print("Demo skipped. You can run it later with: python demo.py")
    
    print("\nNext steps:")
    print("1. Ensure your datasets are properly structured")
    print("2. Run: python train_models.py --model both")
    print("3. Use: python inference.py for predictions")

if __name__ == "__main__":
    main()
