#!/usr/bin/env python3
"""
Inference script for trained CNN-SVM models
"""

import os
import numpy as np
import cv2
import argparse
from cnn_svm_trainer import CNNSVMClassifier
import matplotlib.pyplot as plt

def predict_single_image(model_path, image_path, model_type='model1'):
    """
    Predict class for a single image
    
    Args:
        model_path: Path to saved model (without extension)
        image_path: Path to image file
        model_type: 'model1' for Normal/Dyslexia or 'model2' for A-Z
    """
    # Initialize classifier
    classifier = CNNSVMClassifier(img_size=(224, 224))
    
    # Load trained model
    try:
        classifier.load_model(model_path)
        print(f"Model loaded successfully from: {model_path}")
    except Exception as e:
        print(f"Error loading model: {e}")
        return None
    
    # Load and preprocess image
    try:
        img = classifier.preprocess_image(image_path)
        img_batch = np.expand_dims(img, axis=0)  # Add batch dimension
    except Exception as e:
        print(f"Error loading image: {e}")
        return None
    
    # Make prediction
    try:
        predictions, probabilities = classifier.predict(img_batch)
        predicted_class = predictions[0]
        confidence = np.max(probabilities[0])
        
        # Get all class probabilities
        classes = classifier.label_encoder.classes_
        class_probs = dict(zip(classes, probabilities[0]))
        
        return {
            'predicted_class': predicted_class,
            'confidence': confidence,
            'all_probabilities': class_probs
        }
    except Exception as e:
        print(f"Error making prediction: {e}")
        return None

def predict_batch_images(model_path, images_dir, model_type='model1'):
    """
    Predict classes for multiple images in a directory
    
    Args:
        model_path: Path to saved model (without extension)
        images_dir: Directory containing images
        model_type: 'model1' for Normal/Dyslexia or 'model2' for A-Z
    """
    # Initialize classifier
    classifier = CNNSVMClassifier(img_size=(224, 224))
    
    # Load trained model
    try:
        classifier.load_model(model_path)
        print(f"Model loaded successfully from: {model_path}")
    except Exception as e:
        print(f"Error loading model: {e}")
        return None
    
    # Get all image files
    image_files = [f for f in os.listdir(images_dir) 
                   if f.lower().endswith(('.png', '.jpg', '.jpeg'))]
    
    if not image_files:
        print(f"No image files found in {images_dir}")
        return None
    
    results = []
    
    for img_file in image_files:
        img_path = os.path.join(images_dir, img_file)
        
        try:
            # Load and preprocess image
            img = classifier.preprocess_image(img_path)
            img_batch = np.expand_dims(img, axis=0)
            
            # Make prediction
            predictions, probabilities = classifier.predict(img_batch)
            predicted_class = predictions[0]
            confidence = np.max(probabilities[0])
            
            results.append({
                'filename': img_file,
                'predicted_class': predicted_class,
                'confidence': confidence
            })
            
        except Exception as e:
            print(f"Error processing {img_file}: {e}")
            continue
    
    return results

def visualize_prediction(image_path, prediction_result):
    """Visualize image with prediction"""
    # Load and display image
    img = cv2.imread(image_path)
    img_rgb = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
    
    plt.figure(figsize=(10, 6))
    
    # Display image
    plt.subplot(1, 2, 1)
    plt.imshow(img_rgb)
    plt.title(f"Input Image\n{os.path.basename(image_path)}")
    plt.axis('off')
    
    # Display prediction probabilities
    plt.subplot(1, 2, 2)
    classes = list(prediction_result['all_probabilities'].keys())
    probs = list(prediction_result['all_probabilities'].values())
    
    # Sort by probability
    sorted_indices = np.argsort(probs)[::-1]
    classes = [classes[i] for i in sorted_indices]
    probs = [probs[i] for i in sorted_indices]
    
    # Show top 5 predictions for multi-class, all for binary
    top_n = min(5, len(classes))
    
    plt.barh(range(top_n), probs[:top_n])
    plt.yticks(range(top_n), classes[:top_n])
    plt.xlabel('Probability')
    plt.title(f'Prediction: {prediction_result["predicted_class"]}\n'
              f'Confidence: {prediction_result["confidence"]:.3f}')
    plt.gca().invert_yaxis()
    
    plt.tight_layout()
    plt.show()

def main():
    """Main inference function"""
    parser = argparse.ArgumentParser(description='Run inference with trained CNN-SVM models')
    parser.add_argument('--model_path', required=True,
                       help='Path to saved model (without extension)')
    parser.add_argument('--image_path', 
                       help='Path to single image for prediction')
    parser.add_argument('--images_dir',
                       help='Directory containing multiple images')
    parser.add_argument('--model_type', choices=['model1', 'model2'], default='model1',
                       help='Model type: model1 (Normal/Dyslexia) or model2 (A-Z)')
    parser.add_argument('--visualize', action='store_true',
                       help='Visualize prediction results')
    
    args = parser.parse_args()
    
    if args.image_path:
        # Single image prediction
        print(f"Predicting class for: {args.image_path}")
        result = predict_single_image(args.model_path, args.image_path, args.model_type)
        
        if result:
            print(f"\nPrediction Results:")
            print(f"Predicted Class: {result['predicted_class']}")
            print(f"Confidence: {result['confidence']:.4f}")
            print(f"\nAll Class Probabilities:")
            for class_name, prob in sorted(result['all_probabilities'].items(), 
                                         key=lambda x: x[1], reverse=True):
                print(f"  {class_name}: {prob:.4f}")
            
            if args.visualize:
                visualize_prediction(args.image_path, result)
        
    elif args.images_dir:
        # Batch prediction
        print(f"Predicting classes for images in: {args.images_dir}")
        results = predict_batch_images(args.model_path, args.images_dir, args.model_type)
        
        if results:
            print(f"\nBatch Prediction Results:")
            print("-" * 60)
            for result in results:
                print(f"{result['filename']:<30} | {result['predicted_class']:<15} | "
                      f"{result['confidence']:.4f}")
            
            # Summary statistics
            print(f"\nSummary:")
            print(f"Total images processed: {len(results)}")
            avg_confidence = np.mean([r['confidence'] for r in results])
            print(f"Average confidence: {avg_confidence:.4f}")
            
            # Class distribution
            from collections import Counter
            class_counts = Counter([r['predicted_class'] for r in results])
            print(f"\nPredicted class distribution:")
            for class_name, count in class_counts.items():
                print(f"  {class_name}: {count}")
    
    else:
        print("Please provide either --image_path or --images_dir")

if __name__ == "__main__":
    main()
