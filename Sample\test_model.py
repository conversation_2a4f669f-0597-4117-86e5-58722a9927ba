import tensorflow as tf
import numpy as np
from PIL import Image, ImageEnhance
import os
import sys
import cv2

def load_model():
    """Load the trained dyslexia detection model"""
    try:
        model = tf.keras.models.load_model('dyslexia_model_mobilenetv2.h5')
        print("✅ Model loaded successfully!")
        return model
    except Exception as e:
        print(f"❌ Error loading model: {e}")
        return None

def remove_background(image):
    """Remove background and enhance handwriting visibility"""
    try:
        # Convert PIL to OpenCV format
        img_array = np.array(image)

        # If image is RGB, convert to grayscale
        if len(img_array.shape) == 3:
            gray = cv2.cvtColor(img_array, cv2.COLOR_RGB2GRAY)
        else:
            gray = img_array

        # Apply Gaussian blur to reduce noise
        blurred = cv2.GaussianBlur(gray, (5, 5), 0)

        # Apply adaptive thresholding to separate text from background
        thresh = cv2.adaptiveThreshold(
            blurred, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
            cv2.THRESH_BINARY, 11, 2
        )

        # Invert the image so text is black on white background
        thresh = cv2.bitwise_not(thresh)

        # Apply morphological operations to clean up the image
        kernel = np.ones((2, 2), np.uint8)
        cleaned = cv2.morphologyEx(thresh, cv2.MORPH_CLOSE, kernel)
        cleaned = cv2.morphologyEx(cleaned, cv2.MORPH_OPEN, kernel)

        # Convert back to PIL Image
        processed_img = Image.fromarray(cleaned)

        return processed_img

    except Exception as e:
        print(f"❌ Error removing background: {e}")
        return image.convert('L')

def enhance_contrast(image):
    """Enhance contrast to make handwriting more visible"""
    try:
        # Convert to numpy array
        img_array = np.array(image)

        # Apply CLAHE (Contrast Limited Adaptive Histogram Equalization)
        clahe = cv2.createCLAHE(clipLimit=3.0, tileGridSize=(8, 8))
        enhanced = clahe.apply(img_array)

        # Convert back to PIL
        enhanced_img = Image.fromarray(enhanced)

        # Additional contrast enhancement using PIL
        enhancer = ImageEnhance.Contrast(enhanced_img)
        enhanced_img = enhancer.enhance(1.5)  # Increase contrast by 50%

        return enhanced_img

    except Exception as e:
        print(f"❌ Error enhancing contrast: {e}")
        return image

def preprocess_image(image_path, img_size=224, remove_bg=True, enhance_contrast_flag=True, save_processed=False):
    """
    Preprocess the input image to match the training format
    - Convert to grayscale
    - Optional: Remove background and enhance text
    - Optional: Enhance contrast
    - Resize to 224x224
    - Normalize pixel values to [0,1]
    - Add batch dimension
    """
    try:
        # Load image
        img = Image.open(image_path)

        # Convert to grayscale
        img = img.convert('L')

        print(f"🔧 Processing options:")
        print(f"   Background removal: {'✅' if remove_bg else '❌'}")
        print(f"   Contrast enhancement: {'✅' if enhance_contrast_flag else '❌'}")

        # Apply background removal if requested
        if remove_bg:
            print("🧹 Removing background...")
            img = remove_background(img)

        # Apply contrast enhancement if requested
        if enhance_contrast_flag:
            print("🔆 Enhancing contrast...")
            img = enhance_contrast(img)

        # Save processed image if requested
        if save_processed:
            processed_path = image_path.replace('.', '_processed.')
            img.save(processed_path)
            print(f"💾 Processed image saved as: {processed_path}")

        # Resize to match training size
        img = img.resize((img_size, img_size))

        # Convert to numpy array
        img_array = np.array(img)

        # Normalize to [0,1] range
        img_array = img_array.astype('float32') / 255.0

        # Add channel dimension (for grayscale)
        img_array = np.expand_dims(img_array, axis=-1)

        # Add batch dimension
        img_array = np.expand_dims(img_array, axis=0)

        print(f"✅ Image preprocessed successfully!")
        print(f"   Original image: {image_path}")
        print(f"   Processed shape: {img_array.shape}")

        return img_array

    except Exception as e:
        print(f"❌ Error preprocessing image: {e}")
        return None

def predict_dyslexia(model, image_array):
    """
    Make prediction on the preprocessed image
    Returns: prediction probability and classification
    """
    try:
        # Make prediction
        prediction = model.predict(image_array, verbose=0)
        
        # Get probability (sigmoid output)
        probability = prediction[0][0]
        
        # Classification based on threshold 0.5
        # In binary classification: 0 = dyslexia, 1 = normal
        if probability > 0.5:
            classification = "Normal"
            confidence = probability * 100
        else:
            classification = "Dyslexia"
            confidence = (1 - probability) * 100
            
        return probability, classification, confidence
        
    except Exception as e:
        print(f"❌ Error making prediction: {e}")
        return None, None, None

def test_single_image(image_path, remove_bg=True, enhance_contrast_flag=True, save_processed=False):
    """Test a single image for dyslexia detection with processing options"""
    print("=" * 60)
    print("🧠 DYSLEXIA DETECTION TEST")
    print("=" * 60)

    # Check if image file exists
    if not os.path.exists(image_path):
        print(f"❌ Image file not found: {image_path}")
        return

    # Load model
    model = load_model()
    if model is None:
        return

    # Preprocess image with options
    image_array = preprocess_image(
        image_path,
        remove_bg=remove_bg,
        enhance_contrast_flag=enhance_contrast_flag,
        save_processed=save_processed
    )
    if image_array is None:
        return

    # Make prediction
    probability, classification, confidence = predict_dyslexia(model, image_array)
    if classification is None:
        return

    # Display results
    print("\n" + "=" * 60)
    print("📊 PREDICTION RESULTS")
    print("=" * 60)
    print(f"🖼️  Image: {os.path.basename(image_path)}")
    print(f"🎯 Classification: {classification}")
    print(f"📈 Confidence: {confidence:.2f}%")
    print(f"🔢 Raw Probability: {probability:.4f}")
    print("=" * 60)

    # Interpretation
    print("\n💡 INTERPRETATION:")
    if classification == "Dyslexia":
        print("   The handwriting shows patterns consistent with dyslexia.")
        print("   Consider further assessment by a qualified professional.")
    else:
        print("   The handwriting appears to follow typical patterns.")
        print("   No strong indicators of dyslexia detected.")

    print("\n⚠️  DISCLAIMER:")
    print("   This is an AI model for research purposes only.")
    print("   It should not be used for medical diagnosis.")
    print("   Consult healthcare professionals for proper assessment.")

def test_sample_images():
    """Test with sample images from the dataset"""
    print("🔍 Testing with sample images from dataset...")
    
    # Test with a dyslexia sample
    dyslexia_samples = [
        "final_dataset/train/dyslexia/0_dys_img001-001_png.rf.785602de38ca9386597e71f20e5b7404.jpg",
        "final_dataset/train/dyslexia/1_dys_img002-001_png.rf.70b9966f09ae9c07c55374bd5822970d.jpg"
    ]
    
    # Test with a normal sample
    normal_samples = [
        "final_dataset/train/normal/aa_img001-001_png.rf.b8b8b8b8b8b8b8b8b8b8b8b8b8b8b8b8.jpg",
        "final_dataset/train/normal/bb_img002-001_png.rf.c9c9c9c9c9c9c9c9c9c9c9c9c9c9c9c9.jpg"
    ]
    
    # Find actual files in the directories
    dyslexia_dir = "final_dataset/train/dyslexia"
    normal_dir = "final_dataset/train/normal"
    
    if os.path.exists(dyslexia_dir):
        dyslexia_files = [f for f in os.listdir(dyslexia_dir) if f.endswith('.jpg')][:2]
        for file in dyslexia_files:
            test_single_image(os.path.join(dyslexia_dir, file))
    
    if os.path.exists(normal_dir):
        normal_files = [f for f in os.listdir(normal_dir) if f.endswith('.jpg')][:2]
        for file in normal_files:
            test_single_image(os.path.join(normal_dir, file))

def main():
    """Main function to handle command line arguments"""
    if len(sys.argv) > 1:
        # Test specific image provided as argument
        image_path = sys.argv[1]

        # Check for additional arguments for processing options
        remove_bg = True
        enhance_contrast_flag = True
        save_processed = False

        if len(sys.argv) > 2:
            for arg in sys.argv[2:]:
                if arg == "--no-bg-removal":
                    remove_bg = False
                elif arg == "--no-contrast":
                    enhance_contrast_flag = False
                elif arg == "--save-processed":
                    save_processed = True

        test_single_image(image_path, remove_bg, enhance_contrast_flag, save_processed)
    else:
        # Interactive mode
        print("🧠 Dyslexia Detection Model Tester")
        print("=" * 40)
        print("Options:")
        print("1. Test your own image")
        print("2. Test with sample images")
        print("3. Exit")

        while True:
            choice = input("\nEnter your choice (1-3): ").strip()

            if choice == "1":
                image_path = input("Enter the path to your image: ").strip()

                # Ask for processing options
                print("\n🔧 Processing Options:")
                remove_bg = input("Remove background? (Y/n): ").strip().lower() != 'n'
                enhance_contrast_flag = input("Enhance contrast? (Y/n): ").strip().lower() != 'n'
                save_processed = input("Save processed image? (y/N): ").strip().lower() == 'y'

                test_single_image(image_path, remove_bg, enhance_contrast_flag, save_processed)
                break
            elif choice == "2":
                test_sample_images()
                break
            elif choice == "3":
                print("Goodbye! 👋")
                break
            else:
                print("Invalid choice. Please enter 1, 2, or 3.")

        # Show usage information
        print("\n💡 Command line usage:")
        print("python test_model.py <image_path> [options]")
        print("Options:")
        print("  --no-bg-removal    : Skip background removal")
        print("  --no-contrast      : Skip contrast enhancement")
        print("  --save-processed   : Save the processed image")

if __name__ == "__main__":
    main()
