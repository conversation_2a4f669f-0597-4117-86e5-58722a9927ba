#!/usr/bin/env python3
"""
Quick test script for CNN-SVM implementation
This script creates sample data and tests the complete pipeline
"""

import os
import numpy as np
import matplotlib.pyplot as plt
from cnn_svm_trainer import CNNSVMClassifier
from sklearn.model_selection import train_test_split
import time

def create_sample_data(num_samples=200, img_size=(224, 224)):
    """Create sample data for testing"""
    print("🔧 Creating sample data for testing...")
    
    # Generate random images
    X = np.random.rand(num_samples, img_size[0], img_size[1], 3).astype('float32')
    
    # Generate random binary labels
    y = np.random.choice(['Normal', 'Dyslexia'], size=num_samples)
    
    return X, y

def test_complete_pipeline():
    """Test the complete CNN-SVM pipeline"""
    print("="*60)
    print("🚀 TESTING CNN-SVM PIPELINE")
    print("="*60)
    
    # Create sample data
    X, y = create_sample_data(num_samples=200, img_size=(224, 224))
    
    print(f"📊 Sample data created: {X.shape[0]} images")
    print(f"   Image shape: {X.shape[1:]}")
    print(f"   Classes: {np.unique(y)}")
    
    # Show class distribution
    unique, counts = np.unique(y, return_counts=True)
    for class_name, count in zip(unique, counts):
        print(f"   {class_name}: {count} images")
    
    # Split data
    X_train, X_test, y_train, y_test = train_test_split(
        X, y, test_size=0.3, random_state=42, stratify=y
    )
    
    X_train, X_val, y_train, y_val = train_test_split(
        X_train, y_train, test_size=0.2, random_state=42, stratify=y_train
    )
    
    print(f"\n📂 Data split:")
    print(f"   Training set: {X_train.shape[0]} images")
    print(f"   Validation set: {X_val.shape[0]} images")
    print(f"   Test set: {X_test.shape[0]} images")
    
    # Initialize classifier
    classifier = CNNSVMClassifier(
        img_size=(224, 224),
        use_pretrained=False,  # Use custom CNN for faster testing
        model_type='Custom'
    )
    
    # Train CNN feature extractor
    print("\n🧠 Training CNN feature extractor...")
    start_time = time.time()
    history = classifier.build_and_train_cnn(
        X_train, y_train, X_val, y_val, epochs=3  # Few epochs for demo
    )
    cnn_time = time.time() - start_time
    print(f"⏱️ CNN training completed in {cnn_time:.2f} seconds")
    
    # Train SVM classifier with smaller parameter grid
    print("\n🎯 Training SVM classifier...")
    start_time = time.time()
    
    # Simplified parameter grid for demo
    param_grid = {
        'C': [1, 10],
        'kernel': ['rbf', 'linear'],
        'gamma': ['scale']
    }
    
    best_score = classifier.train_svm(X_train, y_train, param_grid)
    svm_time = time.time() - start_time
    print(f"⏱️ SVM training completed in {svm_time:.2f} seconds")
    
    # Evaluate model
    print("\n📊 Evaluating model on test set...")
    accuracy, report, cm = classifier.evaluate(X_test, y_test)
    
    # Test prediction on single sample
    print("\n🔍 Testing single prediction...")
    sample_image = X_test[:1]  # Take first test image
    predictions, probabilities = classifier.predict(sample_image)
    
    print(f"   Sample prediction: {predictions[0]}")
    print(f"   Prediction probabilities: {probabilities[0]}")
    
    # Save model
    print("\n💾 Saving model...")
    classifier.save_model("quick_test_model")
    
    # Test loading model
    print("📂 Testing model loading...")
    new_classifier = CNNSVMClassifier()
    new_classifier.load_model("quick_test_model")
    
    # Test prediction with loaded model
    new_predictions, new_probabilities = new_classifier.predict(sample_image)
    print(f"   Loaded model prediction: {new_predictions[0]}")
    
    print("\n" + "="*60)
    print("✅ QUICK TEST COMPLETED SUCCESSFULLY!")
    print("="*60)
    print(f"🎯 Final test accuracy: {accuracy:.4f} ({accuracy*100:.2f}%)")
    print(f"⏱️ Total training time: {cnn_time + svm_time:.2f} seconds")
    
    # Clean up test files
    cleanup_files = [
        "quick_test_model_cnn.h5",
        "quick_test_model_svm.pkl", 
        "quick_test_model_scaler.pkl",
        "quick_test_model_label_encoder.pkl"
    ]
    
    print("\n🧹 Cleaning up test files...")
    for file in cleanup_files:
        if os.path.exists(file):
            os.remove(file)
            print(f"   Removed {file}")
    
    return classifier, accuracy

def check_environment():
    """Check if environment is properly set up"""
    print("🔍 Checking environment...")
    
    try:
        import tensorflow as tf
        print(f"✅ TensorFlow: {tf.__version__}")
        
        # Check GPU availability
        gpus = tf.config.list_physical_devices('GPU')
        if gpus:
            print(f"🎮 GPU available: {len(gpus)} device(s)")
        else:
            print("💻 Using CPU (no GPU detected)")
            
    except ImportError:
        print("❌ TensorFlow not installed")
        return False
    
    try:
        import sklearn
        print(f"✅ Scikit-learn: {sklearn.__version__}")
    except ImportError:
        print("❌ Scikit-learn not installed")
        return False
    
    try:
        import cv2
        print(f"✅ OpenCV: {cv2.__version__}")
    except ImportError:
        print("❌ OpenCV not installed")
        return False
    
    try:
        import matplotlib
        print(f"✅ Matplotlib: {matplotlib.__version__}")
    except ImportError:
        print("❌ Matplotlib not installed")
        return False
    
    print("✅ Environment check passed!")
    return True

def main():
    """Main function"""
    print("🧪 CNN-SVM Quick Test")
    print("="*40)
    
    # Check environment
    if not check_environment():
        print("\n❌ Environment check failed!")
        print("Please install required packages: pip install -r requirements.txt")
        return
    
    # Ask user if they want to run the test
    print("\n" + "="*60)
    response = input("🚀 Run quick test with sample data? (y/n): ")
    
    if response.lower() in ['y', 'yes']:
        try:
            classifier, accuracy = test_complete_pipeline()
            
            print("\n🎉 Quick test completed successfully!")
            print("\n📝 What this test verified:")
            print("   ✅ CNN model creation and training")
            print("   ✅ Feature extraction from images")
            print("   ✅ SVM training with hyperparameter tuning")
            print("   ✅ Model evaluation and metrics")
            print("   ✅ Model saving and loading")
            print("   ✅ Prediction pipeline")
            
            print("\n🚀 Ready to train on your actual datasets!")
            print("   Use: python train_models.py --model both")
            print("   Or: Open CNN_SVM_Handwriting_Analysis.ipynb in Jupyter")
            
        except Exception as e:
            print(f"\n❌ Quick test failed with error: {e}")
            print("Please check your installation and try again.")
    else:
        print("Quick test skipped.")
    
    print("\n📚 Available files:")
    print("   📓 CNN_SVM_Handwriting_Analysis.ipynb - Interactive notebook")
    print("   🐍 train_models.py - Training script")
    print("   🔍 inference.py - Prediction script")
    print("   🧪 quick_test.py - This test script")
    print("   📋 requirements.txt - Required packages")

if __name__ == "__main__":
    main()
