import os
import cv2
import shutil

# Adjust this if needed
SOURCE_DIR = "dataset"
OUTPUT_DIR = "cleaned_auto"
BLUR_THRESHOLD = 100.0  # You can increase this if it keeps blurry images

# Create output directory
os.makedirs(OUTPUT_DIR, exist_ok=True)

def is_blurry(img, threshold=BLUR_THRESHOLD):
    gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
    return cv2.Laplacian(gray, cv2.CV_64F).var() < threshold

# Loop through all label folders
for label in os.listdir(SOURCE_DIR):
    label_path = os.path.join(SOURCE_DIR, label)
    if not os.path.isdir(label_path):
        continue

    cleaned_label_path = os.path.join(OUTPUT_DIR, label)
    os.makedirs(cleaned_label_path, exist_ok=True)

    for filename in os.listdir(label_path):
        file_path = os.path.join(label_path, filename)

        try:
            img = cv2.imread(file_path)
            if img is None:
                continue  # Skip broken images

            if not is_blurry(img):
                shutil.copy(file_path, os.path.join(cleaned_label_path, filename))
            else:
                print(f"[BLUR] Removed: {file_path}")

        except Exception as e:
            print(f"[ERROR] {file_path}: {e}")
