import easyocr
import cv2
import matplotlib.pyplot as plt
import numpy as np
from tensorflow.keras.models import load_model

# Load dyslexia classifier
model = load_model('dyslexia_model_mobilenetv2.h5')
class_labels = ['normal', 'dyslexia']

# Initialize OCR reader
reader = easyocr.Reader(['en'], gpu=False)

# Classify individual character image
def classify_letter(char_img):
    resized = cv2.resize(char_img, (224, 224))
    normalized = resized.astype("float32") / 255.0
    normalized = np.expand_dims(normalized, axis=-1)  # grayscale
    normalized = np.expand_dims(normalized, axis=0)   # batch
    prediction = model.predict(normalized)
    label = class_labels[np.argmax(prediction)]
    confidence = np.max(prediction)
    return label, confidence

# Detect and classify each letter
def detect_and_classify_letters(image_path):
    image = cv2.imread(image_path)
    original = image.copy()
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

    results = reader.readtext(image, detail=1)

    for (bbox, word_text, prob) in results:
        (tl, tr, br, bl) = bbox
        x1, y1 = map(int, tl)
        x2, y2 = map(int, br)

        word_crop = gray[y1:y2, x1:x2]

        # Threshold and find contours (letter splitting)
        _, binary = cv2.threshold(word_crop, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)
        contours, _ = cv2.findContours(binary.copy(), cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

        # Sort contours left to right
        contours = sorted(contours, key=lambda c: cv2.boundingRect(c)[0])

        for contour in contours:
            cx, cy, cw, ch = cv2.boundingRect(contour)
            if cw < 5 or ch < 10:
                continue  # skip small noise

            char_img = binary[cy:cy+ch, cx:cx+cw]
            label, conf = classify_letter(char_img)

            # Calculate position on original image
            abs_x = x1 + cx
            abs_y = y1 + cy
            color = (0, 255, 0) if label == 'normal' else (0, 0, 255)

            # Draw box and label
            cv2.rectangle(original, (abs_x, abs_y), (abs_x+cw, abs_y+ch), color, 2)
            cv2.putText(original, f"{label} ({conf:.2f})", (abs_x, abs_y - 5),
                        cv2.FONT_HERSHEY_SIMPLEX, 0.4, color, 1)

    # Show result
    plt.figure(figsize=(12, 6))
    plt.imshow(cv2.cvtColor(original, cv2.COLOR_BGR2RGB))
    plt.title("Letter-by-Letter Detection with Dyslexia Classification")
    plt.axis("off")
    plt.show()

# 🔍 Test
image_path = '1.jpg'  # <- Replace this with your image
detect_and_classify_letters(image_path)
