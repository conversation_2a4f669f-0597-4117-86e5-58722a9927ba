import os
import random
import shutil

def split_data(source_dir, target_dir, split_ratio=(0.8, 0.1, 0.1)):
    classes = ['normal', 'dyslexia']

    for cls in classes:
        src_folder = os.path.join(source_dir, cls)
        files = os.listdir(src_folder)
        random.shuffle(files)

        total = len(files)
        train_end = int(split_ratio[0] * total)
        val_end = train_end + int(split_ratio[1] * total)

        splits = {
            'train': files[:train_end],
            'val': files[train_end:val_end],
            'test': files[val_end:]
        }

        for split_name, split_files in splits.items():
            dest_folder = os.path.join(target_dir, split_name, cls)
            os.makedirs(dest_folder, exist_ok=True)

            for f in split_files:
                src = os.path.join(src_folder, f)
                dst = os.path.join(dest_folder, f)
                shutil.copy(src, dst)

    print("✅ Dataset successfully split into train, val, and test folders.")

# Usage
split_data('binary_dataset', 'final_dataset')
