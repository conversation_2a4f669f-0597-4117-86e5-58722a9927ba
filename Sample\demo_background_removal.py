#!/usr/bin/env python3
"""
Demo script to showcase background removal and contrast enhancement features
for dyslexia detection model.
"""

import os
import sys
from PIL import Image
import matplotlib.pyplot as plt
import numpy as np
import cv2
from PIL import ImageEnhance

def remove_background(image):
    """Remove background and enhance handwriting visibility"""
    try:
        # Convert PIL to OpenCV format
        img_array = np.array(image)
        
        # If image is RGB, convert to grayscale
        if len(img_array.shape) == 3:
            gray = cv2.cvtColor(img_array, cv2.COLOR_RGB2GRAY)
        else:
            gray = img_array
        
        # Apply Gaussian blur to reduce noise
        blurred = cv2.GaussianBlur(gray, (5, 5), 0)
        
        # Apply adaptive thresholding to separate text from background
        thresh = cv2.adaptiveThreshold(
            blurred, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, 
            cv2.THRESH_BINARY, 11, 2
        )
        
        # Invert the image so text is black on white background
        thresh = cv2.bitwise_not(thresh)
        
        # Apply morphological operations to clean up the image
        kernel = np.ones((2, 2), np.uint8)
        cleaned = cv2.morphologyEx(thresh, cv2.MORPH_CLOSE, kernel)
        cleaned = cv2.morphologyEx(cleaned, cv2.MORPH_OPEN, kernel)
        
        # Convert back to PIL Image
        processed_img = Image.fromarray(cleaned)
        
        return processed_img
        
    except Exception as e:
        print(f"❌ Error removing background: {e}")
        return image.convert('L')

def enhance_contrast(image):
    """Enhance contrast to make handwriting more visible"""
    try:
        # Convert to numpy array
        img_array = np.array(image)
        
        # Apply CLAHE (Contrast Limited Adaptive Histogram Equalization)
        clahe = cv2.createCLAHE(clipLimit=3.0, tileGridSize=(8, 8))
        enhanced = clahe.apply(img_array)
        
        # Convert back to PIL
        enhanced_img = Image.fromarray(enhanced)
        
        # Additional contrast enhancement using PIL
        enhancer = ImageEnhance.Contrast(enhanced_img)
        enhanced_img = enhancer.enhance(1.5)  # Increase contrast by 50%
        
        return enhanced_img
        
    except Exception as e:
        print(f"❌ Error enhancing contrast: {e}")
        return image

def create_comparison_demo(image_path, save_demo=True):
    """Create a visual comparison of original vs processed image"""
    try:
        # Load original image
        original = Image.open(image_path)
        
        # Convert to grayscale
        grayscale = original.convert('L')
        
        # Apply background removal
        bg_removed = remove_background(grayscale)
        
        # Apply contrast enhancement
        contrast_enhanced = enhance_contrast(bg_removed)
        
        # Create comparison plot
        fig, axes = plt.subplots(2, 2, figsize=(12, 10))
        fig.suptitle('Dyslexia Detection: Image Processing Pipeline', fontsize=16, fontweight='bold')
        
        # Original image
        axes[0, 0].imshow(original, cmap='gray' if original.mode == 'L' else None)
        axes[0, 0].set_title('1. Original Image', fontweight='bold')
        axes[0, 0].axis('off')
        
        # Grayscale
        axes[0, 1].imshow(grayscale, cmap='gray')
        axes[0, 1].set_title('2. Grayscale Conversion', fontweight='bold')
        axes[0, 1].axis('off')
        
        # Background removed
        axes[1, 0].imshow(bg_removed, cmap='gray')
        axes[1, 0].set_title('3. Background Removal\n(Adaptive Thresholding)', fontweight='bold')
        axes[1, 0].axis('off')
        
        # Final processed
        axes[1, 1].imshow(contrast_enhanced, cmap='gray')
        axes[1, 1].set_title('4. Contrast Enhancement\n(CLAHE + PIL Enhancement)', fontweight='bold')
        axes[1, 1].axis('off')
        
        plt.tight_layout()
        
        if save_demo:
            demo_path = image_path.replace('.jpg', '_processing_demo.png')
            plt.savefig(demo_path, dpi=150, bbox_inches='tight')
            print(f"📊 Demo comparison saved as: {demo_path}")
        
        plt.show()
        
        return {
            'original': original,
            'grayscale': grayscale,
            'background_removed': bg_removed,
            'contrast_enhanced': contrast_enhanced
        }
        
    except Exception as e:
        print(f"❌ Error creating demo: {e}")
        return None

def demo_multiple_samples():
    """Demo with multiple sample images"""
    print("🎨 Background Removal & Contrast Enhancement Demo")
    print("=" * 60)
    
    # Sample images from dataset
    sample_paths = [
        "final_dataset/train/dyslexia/0_dys_img001-001_png.rf.785602de38ca9386597e71f20e5b7404.jpg",
        "final_dataset/train/normal/0_img001-001_png.rf.785602de38ca9386597e71f20e5b7404.jpg"
    ]
    
    for i, path in enumerate(sample_paths):
        if os.path.exists(path):
            print(f"\n🖼️  Processing sample {i+1}: {os.path.basename(path)}")
            create_comparison_demo(path, save_demo=True)
        else:
            print(f"⚠️  Sample not found: {path}")

def main():
    """Main function"""
    if len(sys.argv) > 1:
        # Process specific image
        image_path = sys.argv[1]
        if os.path.exists(image_path):
            print(f"🎨 Creating processing demo for: {image_path}")
            create_comparison_demo(image_path, save_demo=True)
        else:
            print(f"❌ Image not found: {image_path}")
    else:
        # Demo mode with sample images
        demo_multiple_samples()
        
        print("\n💡 Usage:")
        print("python demo_background_removal.py <image_path>")
        print("\nFeatures demonstrated:")
        print("✅ Grayscale conversion")
        print("✅ Adaptive thresholding for background removal")
        print("✅ Morphological operations for noise reduction")
        print("✅ CLAHE contrast enhancement")
        print("✅ PIL contrast boosting")
        print("\n🎯 Benefits:")
        print("• Makes handwriting more visible")
        print("• Removes distracting backgrounds")
        print("• Enhances text clarity for better analysis")
        print("• Improves model accuracy")

if __name__ == "__main__":
    main()
