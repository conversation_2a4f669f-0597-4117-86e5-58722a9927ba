{
 "cells": [
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "# CNN-SVM Handwriting Analysis\n",
    "\n",
    "This notebook implements a hybrid CNN-SVM approach for handwriting analysis with two models:\n",
    "1. **Model 1**: Binary classification (Normal vs Dyslexia)\n",
    "2. **Model 2**: Multi-class classification (A-Z letters)\n",
    "\n",
    "## Architecture\n",
    "- **CNN**: Feature extraction using pre-trained VGG16/ResNet50 or custom CNN\n",
    "- **SVM**: Classification using extracted features with optimized hyperparameters"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 1. Setup and Imports"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Install required packages (run this cell first)\n",
    "!pip install tensorflow scikit-learn opencv-python numpy pandas matplotlib seaborn tqdm joblib Pillow"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "import os\n",
    "import numpy as np\n",
    "import pandas as pd\n",
    "import matplotlib.pyplot as plt\n",
    "import seaborn as sns\n",
    "from sklearn.svm import SVC\n",
    "from sklearn.metrics import classification_report, confusion_matrix, accuracy_score\n",
    "from sklearn.preprocessing import StandardScaler, LabelEncoder\n",
    "from sklearn.model_selection import train_test_split, GridSearchCV\n",
    "import tensorflow as tf\n",
    "from tensorflow.keras.models import Model, Sequential\n",
    "from tensorflow.keras.layers import Conv2D, MaxPooling2D, Flatten, Dense, Dropout, GlobalAveragePooling2D\n",
    "from tensorflow.keras.preprocessing.image import ImageDataGenerator, load_img, img_to_array\n",
    "from tensorflow.keras.applications import VGG16, ResNet50\n",
    "from tensorflow.keras.optimizers import Adam\n",
    "from tensorflow.keras.callbacks import EarlyStopping, ReduceLROnPlateau\n",
    "import cv2\n",
    "from tqdm import tqdm\n",
    "import warnings\n",
    "warnings.filterwarnings('ignore')\n",
    "\n",
    "# Set style for better plots\n",
    "plt.style.use('default')\n",
    "sns.set_palette(\"husl\")\n",
    "\n",
    "print(\"✅ All imports successful!\")\n",
    "print(f\"TensorFlow version: {tf.__version__}\")\n",
    "print(f\"GPU available: {tf.config.list_physical_devices('GPU')}\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 2. CNN-SVM Classifier Class"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "class CNNSVMClassifier:\n",
    "    def __init__(self, img_size=(224, 224), use_pretrained=True, model_type='VGG16'):\n",
    "        \"\"\"\n",
    "        Initialize CNN-SVM Classifier\n",
    "        \n",
    "        Args:\n",
    "            img_size: Input image size (height, width)\n",
    "            use_pretrained: Whether to use pretrained CNN as feature extractor\n",
    "            model_type: Type of pretrained model ('VGG16', 'ResNet50', or 'Custom')\n",
    "        \"\"\"\n",
    "        self.img_size = img_size\n",
    "        self.use_pretrained = use_pretrained\n",
    "        self.model_type = model_type\n",
    "        self.cnn_model = None\n",
    "        self.svm_model = None\n",
    "        self.scaler = StandardScaler()\n",
    "        self.label_encoder = LabelEncoder()\n",
    "        \n",
    "    def build_cnn_feature_extractor(self, input_shape, num_classes=None):\n",
    "        \"\"\"Build CNN model for feature extraction\"\"\"\n",
    "        if self.use_pretrained and self.model_type == 'VGG16':\n",
    "            # Use VGG16 as feature extractor\n",
    "            base_model = VGG16(weights='imagenet', include_top=False, input_shape=input_shape)\n",
    "            base_model.trainable = False  # Freeze base model\n",
    "            \n",
    "            model = Sequential([\n",
    "                base_model,\n",
    "                GlobalAveragePooling2D(),\n",
    "                Dense(512, activation='relu'),\n",
    "                Dropout(0.5),\n",
    "                Dense(256, activation='relu'),\n",
    "                Dropout(0.3)\n",
    "            ])\n",
    "            \n",
    "        elif self.use_pretrained and self.model_type == 'ResNet50':\n",
    "            # Use ResNet50 as feature extractor\n",
    "            base_model = ResNet50(weights='imagenet', include_top=False, input_shape=input_shape)\n",
    "            base_model.trainable = False\n",
    "            \n",
    "            model = Sequential([\n",
    "                base_model,\n",
    "                GlobalAveragePooling2D(),\n",
    "                Dense(512, activation='relu'),\n",
    "                Dropout(0.5),\n",
    "                Dense(256, activation='relu'),\n",
    "                Dropout(0.3)\n",
    "            ])\n",
    "            \n",
    "        else:\n",
    "            # Custom CNN architecture\n",
    "            model = Sequential([\n",
    "                Conv2D(32, (3, 3), activation='relu', input_shape=input_shape),\n",
    "                MaxPooling2D((2, 2)),\n",
    "                Conv2D(64, (3, 3), activation='relu'),\n",
    "                MaxPooling2D((2, 2)),\n",
    "                Conv2D(128, (3, 3), activation='relu'),\n",
    "                MaxPooling2D((2, 2)),\n",
    "                Conv2D(256, (3, 3), activation='relu'),\n",
    "                MaxPooling2D((2, 2)),\n",
    "                Flatten(),\n",
    "                Dense(512, activation='relu'),\n",
    "                Dropout(0.5),\n",
    "                Dense(256, activation='relu'),\n",
    "                Dropout(0.3)\n",
    "            ])\n",
    "        \n",
    "        return model\n",
    "    \n",
    "    def preprocess_image(self, img_path):\n",
    "        \"\"\"Preprocess individual image\"\"\"\n",
    "        img = cv2.imread(img_path)\n",
    "        if img is None:\n",
    "            raise ValueError(f\"Could not load image: {img_path}\")\n",
    "            \n",
    "        img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)\n",
    "        img = cv2.resize(img, self.img_size)\n",
    "        img = img.astype('float32') / 255.0\n",
    "        return img\n",
    "    \n",
    "    def load_and_preprocess_data(self, data_path, dataset_type='model1', max_samples_per_class=None):\n",
    "        \"\"\"Load and preprocess image data\"\"\"\n",
    "        images = []\n",
    "        labels = []\n",
    "        \n",
    "        if dataset_type == 'model1':\n",
    "            # Binary classification: Normal vs Dyslexia\n",
    "            for split in ['Train', 'test']:\n",
    "                split_path = os.path.join(data_path, split)\n",
    "                if not os.path.exists(split_path):\n",
    "                    continue\n",
    "                    \n",
    "                for class_name in ['Normal', 'Dyslexia']:\n",
    "                    class_path = os.path.join(split_path, class_name)\n",
    "                    if not os.path.exists(class_path):\n",
    "                        continue\n",
    "                    \n",
    "                    class_count = 0\n",
    "                    # Process all letter subdirectories\n",
    "                    for letter_dir in os.listdir(class_path):\n",
    "                        letter_path = os.path.join(class_path, letter_dir)\n",
    "                        if os.path.isdir(letter_path):\n",
    "                            for img_file in tqdm(os.listdir(letter_path), \n",
    "                                               desc=f\"Loading {split}/{class_name}/{letter_dir}\"):\n",
    "                                if max_samples_per_class and class_count >= max_samples_per_class:\n",
    "                                    break\n",
    "                                if img_file.lower().endswith(('.png', '.jpg', '.jpeg')):\n",
    "                                    img_path = os.path.join(letter_path, img_file)\n",
    "                                    try:\n",
    "                                        img = self.preprocess_image(img_path)\n",
    "                                        images.append(img)\n",
    "                                        labels.append(class_name)\n",
    "                                        class_count += 1\n",
    "                                    except Exception as e:\n",
    "                                        print(f\"Error loading {img_path}: {e}\")\n",
    "                            if max_samples_per_class and class_count >= max_samples_per_class:\n",
    "                                break\n",
    "                                        \n",
    "        elif dataset_type == 'model2':\n",
    "            # Multi-class classification: A-Z letters\n",
    "            for split in ['train', 'test']:\n",
    "                split_path = os.path.join(data_path, split)\n",
    "                if not os.path.exists(split_path):\n",
    "                    continue\n",
    "                    \n",
    "                for letter in 'ABCDEFGHIJKLMNOPQRSTUVWXYZ':\n",
    "                    letter_path = os.path.join(split_path, letter)\n",
    "                    if not os.path.exists(letter_path):\n",
    "                        continue\n",
    "                    \n",
    "                    letter_count = 0\n",
    "                    for img_file in tqdm(os.listdir(letter_path), \n",
    "                                       desc=f\"Loading {split}/{letter}\"):\n",
    "                        if max_samples_per_class and letter_count >= max_samples_per_class:\n",
    "                            break\n",
    "                        if img_file.lower().endswith(('.png', '.jpg', '.jpeg')):\n",
    "                            img_path = os.path.join(letter_path, img_file)\n",
    "                            try:\n",
    "                                img = self.preprocess_image(img_path)\n",
    "                                images.append(img)\n",
    "                                labels.append(letter)\n",
    "                                letter_count += 1\n",
    "                            except Exception as e:\n",
    "                                print(f\"Error loading {img_path}: {e}\")\n",
    "        \n",
    "        return np.array(images), np.array(labels)\n",
    "    \n",
    "    def extract_features(self, images):\n",
    "        \"\"\"Extract features using CNN\"\"\"\n",
    "        if self.cnn_model is None:\n",
    "            raise ValueError(\"CNN model not built. Call build_and_train_cnn first.\")\n",
    "        \n",
    "        features = self.cnn_model.predict(images, batch_size=32, verbose=1)\n",
    "        return features\n",
    "    \n",
    "    def build_and_train_cnn(self, X_train, y_train, X_val=None, y_val=None, epochs=50):\n",
    "        \"\"\"Build and train CNN feature extractor\"\"\"\n",
    "        input_shape = X_train.shape[1:]\n",
    "        num_classes = len(np.unique(y_train))\n",
    "        \n",
    "        # Build CNN model\n",
    "        self.cnn_model = self.build_cnn_feature_extractor(input_shape, num_classes)\n",
    "        \n",
    "        print(\"CNN Model Architecture:\")\n",
    "        self.cnn_model.summary()\n",
    "        \n",
    "        # Compile model\n",
    "        self.cnn_model.compile(\n",
    "            optimizer=Adam(learning_rate=0.001),\n",
    "            loss='sparse_categorical_crossentropy',\n",
    "            metrics=['accuracy']\n",
    "        )\n",
    "        \n",
    "        # Encode labels\n",
    "        y_train_encoded = self.label_encoder.fit_transform(y_train)\n",
    "        if y_val is not None:\n",
    "            y_val_encoded = self.label_encoder.transform(y_val)\n",
    "        \n",
    "        # Callbacks\n",
    "        callbacks = [\n",
    "            EarlyStopping(patience=10, restore_best_weights=True),\n",
    "            ReduceLROnPlateau(factor=0.5, patience=5)\n",
    "        ]\n",
    "        \n",
    "        # Train CNN\n",
    "        if X_val is not None and y_val is not None:\n",
    "            history = self.cnn_model.fit(\n",
    "                X_train, y_train_encoded,\n",
    "                validation_data=(X_val, y_val_encoded),\n",
    "                epochs=epochs,\n",
    "                batch_size=32,\n",
    "                callbacks=callbacks,\n",
    "                verbose=1\n",
    "            )\n",
    "        else:\n",
    "            history = self.cnn_model.fit(\n",
    "                X_train, y_train_encoded,\n",
    "                epochs=epochs,\n",
    "                batch_size=32,\n",
    "                callbacks=callbacks,\n",
    "                verbose=1\n",
    "            )\n",
    "        \n",
    "        return history\n",
    "    \n",
    "    def train_svm(self, X_train, y_train, param_grid=None):\n",
    "        \"\"\"Train SVM classifier on CNN features\"\"\"\n",
    "        if param_grid is None:\n",
    "            param_grid = {\n",
    "                'C': [0.1, 1, 10, 100],\n",
    "                'kernel': ['rbf', 'linear'],\n",
    "                'gamma': ['scale', 'auto', 0.001, 0.01, 0.1, 1]\n",
    "            }\n",
    "        \n",
    "        # Extract features using CNN\n",
    "        print(\"Extracting features for SVM training...\")\n",
    "        features = self.extract_features(X_train)\n",
    "        \n",
    "        # Scale features\n",
    "        features_scaled = self.scaler.fit_transform(features)\n",
    "        \n",
    "        # Grid search for best SVM parameters\n",
    "        print(\"Performing grid search for SVM hyperparameters...\")\n",
    "        svm = SVC(probability=True, random_state=42)\n",
    "        grid_search = GridSearchCV(svm, param_grid, cv=5, scoring='accuracy', n_jobs=-1, verbose=1)\n",
    "        grid_search.fit(features_scaled, y_train)\n",
    "        \n",
    "        self.svm_model = grid_search.best_estimator_\n",
    "        \n",
    "        print(f\"Best SVM parameters: {grid_search.best_params_}\")\n",
    "        print(f\"Best cross-validation score: {grid_search.best_score_:.4f}\")\n",
    "        \n",
    "        return grid_search.best_score_\n",
    "    \n",
    "    def predict(self, X_test):\n",
    "        \"\"\"Make predictions using CNN-SVM pipeline\"\"\"\n",
    "        # Extract features\n",
    "        features = self.extract_features(X_test)\n",
    "        \n",
    "        # Scale features\n",
    "        features_scaled = self.scaler.transform(features)\n",
    "        \n",
    "        # Predict using SVM\n",
    "        predictions = self.svm_model.predict(features_scaled)\n",
    "        probabilities = self.svm_model.predict_proba(features_scaled)\n",
    "        \n",
    "        return predictions, probabilities\n",
    "    \n",
    "    def evaluate(self, X_test, y_test):\n",
    "        \"\"\"Evaluate model performance\"\"\"\n",
    "        predictions, probabilities = self.predict(X_test)\n",
    "        \n",
    "        # Calculate metrics\n",
    "        accuracy = accuracy_score(y_test, predictions)\n",
    "        report = classification_report(y_test, predictions)\n",
    "        cm = confusion_matrix(y_test, predictions)\n",
    "        \n",
    "        print(f\"Test Accuracy: {accuracy:.4f}\")\n",
    "        print(\"\\nClassification Report:\")\n",
    "        print(report)\n",
    "        \n",
    "        # Plot confusion matrix\n",
    "        plt.figure(figsize=(10, 8))\n",
    "        sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', \n",
    "                   xticklabels=self.label_encoder.classes_,\n",
    "                   yticklabels=self.label_encoder.classes_)\n",
    "        plt.title('Confusion Matrix')\n",
    "        plt.ylabel('True Label')\n",
    "        plt.xlabel('Predicted Label')\n",
    "        plt.tight_layout()\n",
    "        plt.show()\n",
    "        \n",
    "        return accuracy, report, cm\n",
    "    \n",
    "    def save_model(self, filepath):\n",
    "        \"\"\"Save trained models\"\"\"\n",
    "        import joblib\n",
    "        \n",
    "        # Save CNN model\n",
    "        self.cnn_model.save(f\"{filepath}_cnn.h5\")\n",
    "        \n",
    "        # Save SVM model and scaler\n",
    "        joblib.dump(self.svm_model, f\"{filepath}_svm.pkl\")\n",
    "        joblib.dump(self.scaler, f\"{filepath}_scaler.pkl\")\n",
    "        joblib.dump(self.label_encoder, f\"{filepath}_label_encoder.pkl\")\n",
    "        \n",
    "        print(f\"Models saved with prefix: {filepath}\")\n",
    "    \n",
    "    def load_model(self, filepath):\n",
    "        \"\"\"Load trained models\"\"\"\n",
    "        import joblib\n",
    "        from tensorflow.keras.models import load_model\n",
    "        \n",
    "        # Load CNN model\n",
    "        self.cnn_model = load_model(f\"{filepath}_cnn.h5\")\n",
    "        \n",
    "        # Load SVM model and scaler\n",
    "        self.svm_model = joblib.load(f\"{filepath}_svm.pkl\")\n",
    "        self.scaler = joblib.load(f\"{filepath}_scaler.pkl\")\n",
    "        self.label_encoder = joblib.load(f\"{filepath}_label_encoder.pkl\")\n",
    "        \n",
    "        print(f\"Models loaded from: {filepath}\")\n",
    "\n",
    "print(\"✅ CNNSVMClassifier class defined successfully!\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 3. Utility Functions"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "def plot_training_history(history, model_name):\n",
    "    \"\"\"Plot training history\"\"\"\n",
    "    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 5))\n",
    "    \n",
    "    # Plot accuracy\n",
    "    ax1.plot(history.history['accuracy'], label='Training Accuracy', linewidth=2)\n",
    "    if 'val_accuracy' in history.history:\n",
    "        ax1.plot(history.history['val_accuracy'], label='Validation Accuracy', linewidth=2)\n",
    "    ax1.set_title(f'{model_name} - Model Accuracy', fontsize=14, fontweight='bold')\n",
    "    ax1.set_xlabel('Epoch')\n",
    "    ax1.set_ylabel('Accuracy')\n",
    "    ax1.legend()\n",
    "    ax1.grid(True, alpha=0.3)\n",
    "    \n",
    "    # Plot loss\n",
    "    ax2.plot(history.history['loss'], label='Training Loss', linewidth=2)\n",
    "    if 'val_loss' in history.history:\n",
    "        ax2.plot(history.history['val_loss'], label='Validation Loss', linewidth=2)\n",
    "    ax2.set_title(f'{model_name} - Model Loss', fontsize=14, fontweight='bold')\n",
    "    ax2.set_xlabel('Epoch')\n",
    "    ax2.set_ylabel('Loss')\n",
    "    ax2.legend()\n",
    "    ax2.grid(True, alpha=0.3)\n",
    "    \n",
    "    plt.tight_layout()\n",
    "    plt.show()\n",
    "\n",
    "def check_dataset_structure():\n",
    "    \"\"\"Check if datasets are properly structured\"\"\"\n",
    "    print(\"📁 Checking dataset structure...\")\n",
    "    \n",
    "    datasets_found = []\n",
    "    \n",
    "    # Check Model 1\n",
    "    model1_path = \"Model 1 Normal and dyslexia\"\n",
    "    if os.path.exists(model1_path):\n",
    "        print(f\"✅ Found {model1_path}\")\n",
    "        datasets_found.append('model1')\n",
    "        \n",
    "        # Count samples\n",
    "        train_path = os.path.join(model1_path, \"Train\")\n",
    "        if os.path.exists(train_path):\n",
    "            normal_count = 0\n",
    "            dyslexia_count = 0\n",
    "            \n",
    "            normal_path = os.path.join(train_path, \"Normal\")\n",
    "            if os.path.exists(normal_path):\n",
    "                for letter_dir in os.listdir(normal_path):\n",
    "                    letter_path = os.path.join(normal_path, letter_dir)\n",
    "                    if os.path.isdir(letter_path):\n",
    "                        normal_count += len([f for f in os.listdir(letter_path) \n",
    "                                           if f.lower().endswith(('.png', '.jpg', '.jpeg'))])\n",
    "            \n",
    "            dyslexia_path = os.path.join(train_path, \"Dyslexia\")\n",
    "            if os.path.exists(dyslexia_path):\n",
    "                for letter_dir in os.listdir(dyslexia_path):\n",
    "                    letter_path = os.path.join(dyslexia_path, letter_dir)\n",
    "                    if os.path.isdir(letter_path):\n",
    "                        dyslexia_count += len([f for f in os.listdir(letter_path) \n",
    "                                             if f.lower().endswith(('.png', '.jpg', '.jpeg'))])\n",
    "            \n",
    "            print(f\"   📊 Normal samples: {normal_count}\")\n",
    "            print(f\"   📊 Dyslexia samples: {dyslexia_count}\")\n",
    "    else:\n",
    "        print(f\"❌ Dataset not found: {model1_path}\")\n",
    "    \n",
    "    # Check Model 2\n",
    "    model2_path = \"Model 2 Alphabets 26 letters\"\n",
    "    if os.path.exists(model2_path):\n",
    "        print(f\"✅ Found {model2_path}\")\n",
    "        datasets_found.append('model2')\n",
    "        \n",
    "        # Count samples\n",
    "        train_path = os.path.join(model2_path, \"train\")\n",
    "        if os.path.exists(train_path):\n",
    "            total_samples = 0\n",
    "            letter_counts = {}\n",
    "            \n",
    "            for letter in 'ABCDEFGHIJKLMNOPQRSTUVWXYZ':\n",
    "                letter_path = os.path.join(train_path, letter)\n",
    "                if os.path.exists(letter_path):\n",
    "                    count = len([f for f in os.listdir(letter_path) \n",
    "                               if f.lower().endswith(('.png', '.jpg', '.jpeg'))])\n",
    "                    letter_counts[letter] = count\n",
    "                    total_samples += count\n",
    "            \n",
    "            print(f\"   📊 Total samples: {total_samples}\")\n",
    "            print(f\"   📊 Letters found: {len(letter_counts)}\")\n",
    "            if letter_counts:\n",
    "                avg_per_letter = total_samples / len(letter_counts)\n",
    "                print(f\"   📊 Average per letter: {avg_per_letter:.1f}\")\n",
    "    else:\n",
    "        print(f\"❌ Dataset not found: {model2_path}\")\n",
    "    \n",
    "    return datasets_found\n",
    "\n",
    "def visualize_sample_images(X, y, num_samples=8, title=\"Sample Images\"):\n",
    "    \"\"\"Visualize sample images from dataset\"\"\"\n",
    "    fig, axes = plt.subplots(2, 4, figsize=(15, 8))\n",
    "    axes = axes.ravel()\n",
    "    \n",
    "    indices = np.random.choice(len(X), num_samples, replace=False)\n",
    "    \n",
    "    for i, idx in enumerate(indices):\n",
    "        axes[i].imshow(X[idx])\n",
    "        axes[i].set_title(f'Class: {y[idx]}', fontsize=12)\n",
    "        axes[i].axis('off')\n",
    "    \n",
    "    plt.suptitle(title, fontsize=16, fontweight='bold')\n",
    "    plt.tight_layout()\n",
    "    plt.show()\n",
    "\n",
    "print(\"✅ Utility functions defined successfully!\")"
   ]
  }
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 4. Check Dataset and Environment"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Check available datasets\n",
    "available_datasets = check_dataset_structure()\n",
    "print(f\"\\n📋 Available datasets: {available_datasets}\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 5. Model 1: Normal vs Dyslexia Classification\n",
    "\n",
    "Let's train the first model for binary classification between Normal and Dyslexia handwriting."
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Configuration for Model 1\n",
    "MODEL1_CONFIG = {\n",
    "    'data_path': 'Model 1 Normal and dyslexia',\n",
    "    'img_size': (224, 224),\n",
    "    'use_pretrained': True,\n",
    "    'model_type': 'VGG16',  # Options: 'VGG16', 'ResNet50', 'Custom'\n",
    "    'epochs': 20,\n",
    "    'max_samples_per_class': 1000  # Limit samples for faster training (set to None for all)\n",
    "}\n",
    "\n",
    "print(\"🔧 Model 1 Configuration:\")\n",
    "for key, value in MODEL1_CONFIG.items():\n",
    "    print(f\"   {key}: {value}\")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Check if Model 1 dataset is available\n",
    "if 'model1' in available_datasets:\n",
    "    print(\"🚀 Starting Model 1 training...\")\n",
    "    \n",
    "    # Initialize classifier\n",
    "    model1_classifier = CNNSVMClassifier(\n",
    "        img_size=MODEL1_CONFIG['img_size'],\n",
    "        use_pretrained=MODEL1_CONFIG['use_pretrained'],\n",
    "        model_type=MODEL1_CONFIG['model_type']\n",
    "    )\n",
    "    \n",
    "    # Load data\n",
    "    print(\"📂 Loading Model 1 dataset...\")\n",
    "    X1, y1 = model1_classifier.load_and_preprocess_data(\n",
    "        MODEL1_CONFIG['data_path'], \n",
    "        dataset_type='model1',\n",
    "        max_samples_per_class=MODEL1_CONFIG['max_samples_per_class']\n",
    "    )\n",
    "    \n",
    "    print(f\"\\n📊 Dataset Summary:\")\n",
    "    print(f\"   Total images: {X1.shape[0]}\")\n",
    "    print(f\"   Image shape: {X1.shape[1:]}\")\n",
    "    print(f\"   Classes: {np.unique(y1)}\")\n",
    "    \n",
    "    # Show class distribution\n",
    "    unique, counts = np.unique(y1, return_counts=True)\n",
    "    for class_name, count in zip(unique, counts):\n",
    "        print(f\"   {class_name}: {count} images\")\n",
    "    \n",
    "else:\n",
    "    print(\"❌ Model 1 dataset not found. Please check the dataset structure.\")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Visualize sample images from Model 1\n",
    "if 'model1' in available_datasets and 'X1' in locals():\n",
    "    visualize_sample_images(X1, y1, num_samples=8, title=\"Model 1: Sample Images (Normal vs Dyslexia)\")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Split data for Model 1\n",
    "if 'model1' in available_datasets and 'X1' in locals():\n",
    "    print(\"🔄 Splitting Model 1 data...\")\n",
    "    \n",
    "    # Split data\n",
    "    X1_train, X1_test, y1_train, y1_test = train_test_split(\n",
    "        X1, y1, test_size=0.2, random_state=42, stratify=y1\n",
    "    )\n",
    "    \n",
    "    X1_train, X1_val, y1_train, y1_val = train_test_split(\n",
    "        X1_train, y1_train, test_size=0.2, random_state=42, stratify=y1_train\n",
    "    )\n",
    "    \n",
    "    print(f\"   Training set: {X1_train.shape[0]} images\")\n",
    "    print(f\"   Validation set: {X1_val.shape[0]} images\")\n",
    "    print(f\"   Test set: {X1_test.shape[0]} images\")\n",
    "    \n",
    "    # Show split distribution\n",
    "    print(\"\\n📊 Split Distribution:\")\n",
    "    for split_name, y_split in [('Train', y1_train), ('Validation', y1_val), ('Test', y1_test)]:\n",
    "        unique, counts = np.unique(y_split, return_counts=True)\n",
    "        print(f\"   {split_name}: {dict(zip(unique, counts))}\")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Train CNN for Model 1\n",
    "if 'model1' in available_datasets and 'X1_train' in locals():\n",
    "    print(\"🧠 Training CNN feature extractor for Model 1...\")\n",
    "    \n",
    "    import time\n",
    "    start_time = time.time()\n",
    "    \n",
    "    history1 = model1_classifier.build_and_train_cnn(\n",
    "        X1_train, y1_train, X1_val, y1_val, \n",
    "        epochs=MODEL1_CONFIG['epochs']\n",
    "    )\n",
    "    \n",
    "    cnn_time = time.time() - start_time\n",
    "    print(f\"\\n⏱️ CNN training completed in {cnn_time:.2f} seconds\")\n",
    "    \n",
    "    # Plot training history\n",
    "    plot_training_history(history1, \"Model 1 CNN (Normal vs Dyslexia)\")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Train SVM for Model 1\n",
    "if 'model1' in available_datasets and 'model1_classifier' in locals() and model1_classifier.cnn_model is not None:\n",
    "    print(\"🎯 Training SVM classifier for Model 1...\")\n",
    "    \n",
    "    start_time = time.time()\n",
    "    \n",
    "    # Simplified parameter grid for faster training\n",
    "    param_grid1 = {\n",
    "        'C': [1, 10, 100],\n",
    "        'kernel': ['rbf', 'linear'],\n",
    "        'gamma': ['scale', 'auto']\n",
    "    }\n",
    "    \n",
    "    best_score1 = model1_classifier.train_svm(X1_train, y1_train, param_grid1)\n",
    "    \n",
    "    svm_time = time.time() - start_time\n",
    "    print(f\"\\n⏱️ SVM training completed in {svm_time:.2f} seconds\")\n",
    "    print(f\"🎯 Best cross-validation score: {best_score1:.4f}\")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Evaluate Model 1\n",
    "if 'model1' in available_datasets and 'model1_classifier' in locals() and model1_classifier.svm_model is not None:\n",
    "    print(\"📊 Evaluating Model 1 on test set...\")\n",
    "    \n",
    "    accuracy1, report1, cm1 = model1_classifier.evaluate(X1_test, y1_test)\n",
    "    \n",
    "    print(f\"\\n🎉 Model 1 Final Results:\")\n",
    "    print(f\"   Test Accuracy: {accuracy1:.4f} ({accuracy1*100:.2f}%)\")\n",
    "    \n",
    "    # Save Model 1\n",
    "    model1_classifier.save_model(\"model1_normal_dyslexia\")\n",
    "    print(\"💾 Model 1 saved successfully!\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 6. Model 2: A-Z Alphabet Classification\n",
    "\n",
    "Now let's train the second model for multi-class classification of alphabet letters."
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Configuration for Model 2\n",
    "MODEL2_CONFIG = {\n",
    "    'data_path': 'Model 2 Alphabets 26 letters',\n",
    "    'img_size': (224, 224),\n",
    "    'use_pretrained': True,\n",
    "    'model_type': 'VGG16',  # Options: 'VGG16', 'ResNet50', 'Custom'\n",
    "    'epochs': 20,\n",
    "    'max_samples_per_class': 100  # Limit samples for faster training (set to None for all)\n",
    "}\n",
    "\n",
    "print(\"🔧 Model 2 Configuration:\")\n",
    "for key, value in MODEL2_CONFIG.items():\n",
    "    print(f\"   {key}: {value}\")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Check if Model 2 dataset is available\n",
    "if 'model2' in available_datasets:\n",
    "    print(\"🚀 Starting Model 2 training...\")\n",
    "    \n",
    "    # Initialize classifier\n",
    "    model2_classifier = CNNSVMClassifier(\n",
    "        img_size=MODEL2_CONFIG['img_size'],\n",
    "        use_pretrained=MODEL2_CONFIG['use_pretrained'],\n",
    "        model_type=MODEL2_CONFIG['model_type']\n",
    "    )\n",
    "    \n",
    "    # Load data\n",
    "    print(\"📂 Loading Model 2 dataset...\")\n",
    "    X2, y2 = model2_classifier.load_and_preprocess_data(\n",
    "        MODEL2_CONFIG['data_path'], \n",
    "        dataset_type='model2',\n",
    "        max_samples_per_class=MODEL2_CONFIG['max_samples_per_class']\n",
    "    )\n",
    "    \n",
    "    print(f\"\\n📊 Dataset Summary:\")\n",
    "    print(f\"   Total images: {X2.shape[0]}\")\n",
    "    print(f\"   Image shape: {X2.shape[1:]}\")\n",
    "    print(f\"   Number of classes: {len(np.unique(y2))}\")\n",
    "    print(f\"   Classes: {sorted(np.unique(y2))}\")\n",
    "    \n",
    "    # Show class distribution\n",
    "    unique, counts = np.unique(y2, return_counts=True)\n",
    "    print(f\"\\n📊 Class Distribution:\")\n",
    "    for class_name, count in zip(unique, counts):\n",
    "        print(f\"   {class_name}: {count} images\")\n",
    "    \n",
    "else:\n",
    "    print(\"❌ Model 2 dataset not found. Please check the dataset structure.\")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Visualize sample images from Model 2\n",
    "if 'model2' in available_datasets and 'X2' in locals():\n",
    "    visualize_sample_images(X2, y2, num_samples=8, title=\"Model 2: Sample Images (A-Z Letters)\")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Split data for Model 2\n",
    "if 'model2' in available_datasets and 'X2' in locals():\n",
    "    print(\"🔄 Splitting Model 2 data...\")\n",
    "    \n",
    "    # Split data\n",
    "    X2_train, X2_test, y2_train, y2_test = train_test_split(\n",
    "        X2, y2, test_size=0.2, random_state=42, stratify=y2\n",
    "    )\n",
    "    \n",
    "    X2_train, X2_val, y2_train, y2_val = train_test_split(\n",
    "        X2_train, y2_train, test_size=0.2, random_state=42, stratify=y2_train\n",
    "    )\n",
    "    \n",
    "    print(f\"   Training set: {X2_train.shape[0]} images\")\n",
    "    print(f\"   Validation set: {X2_val.shape[0]} images\")\n",
    "    print(f\"   Test set: {X2_test.shape[0]} images\")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Train CNN for Model 2\n",
    "if 'model2' in available_datasets and 'X2_train' in locals():\n",
    "    print(\"🧠 Training CNN feature extractor for Model 2...\")\n",
    "    \n",
    "    start_time = time.time()\n",
    "    \n",
    "    history2 = model2_classifier.build_and_train_cnn(\n",
    "        X2_train, y2_train, X2_val, y2_val, \n",
    "        epochs=MODEL2_CONFIG['epochs']\n",
    "    )\n",
    "    \n",
    "    cnn_time = time.time() - start_time\n",
    "    print(f\"\\n⏱️ CNN training completed in {cnn_time:.2f} seconds\")\n",
    "    \n",
    "    # Plot training history\n",
    "    plot_training_history(history2, \"Model 2 CNN (A-Z Letters)\")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Train SVM for Model 2\n",
    "if 'model2' in available_datasets and 'model2_classifier' in locals() and model2_classifier.cnn_model is not None:\n",
    "    print(\"🎯 Training SVM classifier for Model 2...\")\n",
    "    \n",
    "    start_time = time.time()\n",
    "    \n",
    "    # Simplified parameter grid for multi-class problem\n",
    "    param_grid2 = {\n",
    "        'C': [1, 10, 100],\n",
    "        'kernel': ['rbf', 'linear'],\n",
    "        'gamma': ['scale', 'auto']\n",
    "    }\n",
    "    \n",
    "    best_score2 = model2_classifier.train_svm(X2_train, y2_train, param_grid2)\n",
    "    \n",
    "    svm_time = time.time() - start_time\n",
    "    print(f\"\\n⏱️ SVM training completed in {svm_time:.2f} seconds\")\n",
    "    print(f\"🎯 Best cross-validation score: {best_score2:.4f}\")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Evaluate Model 2\n",
    "if 'model2' in available_datasets and 'model2_classifier' in locals() and model2_classifier.svm_model is not None:\n",
    "    print(\"📊 Evaluating Model 2 on test set...\")\n",
    "    \n",
    "    accuracy2, report2, cm2 = model2_classifier.evaluate(X2_test, y2_test)\n",
    "    \n",
    "    print(f\"\\n🎉 Model 2 Final Results:\")\n",
    "    print(f\"   Test Accuracy: {accuracy2:.4f} ({accuracy2*100:.2f}%)\")\n",
    "    \n",
    "    # Save Model 2\n",
    "    model2_classifier.save_model(\"model2_alphabet_letters\")\n",
    "    print(\"💾 Model 2 saved successfully!\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 7. Testing and Inference\n",
    "\n",
    "Let's test our trained models with some predictions."
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "def test_single_prediction(classifier, X_test, y_test, model_name, num_samples=5):\n",
    "    \"\"\"Test predictions on random samples\"\"\"\n",
    "    print(f\"🔍 Testing {model_name} predictions...\")\n",
    "    \n",
    "    # Select random samples\n",
    "    indices = np.random.choice(len(X_test), num_samples, replace=False)\n",
    "    \n",
    "    fig, axes = plt.subplots(1, num_samples, figsize=(15, 3))\n",
    "    if num_samples == 1:\n",
    "        axes = [axes]\n",
    "    \n",
    "    for i, idx in enumerate(indices):\n",
    "        # Make prediction\n",
    "        sample = X_test[idx:idx+1]\n",
    "        predictions, probabilities = classifier.predict(sample)\n",
    "        \n",
    "        predicted_class = predictions[0]\n",
    "        true_class = y_test[idx]\n",
    "        confidence = np.max(probabilities[0])\n",
    "        \n",
    "        # Display image\n",
    "        axes[i].imshow(X_test[idx])\n",
    "        \n",
    "        # Set title with prediction info\n",
    "        color = 'green' if predicted_class == true_class else 'red'\n",
    "        axes[i].set_title(f'True: {true_class}\\nPred: {predicted_class}\\nConf: {confidence:.3f}', \n",
    "                         color=color, fontsize=10)\n",
    "        axes[i].axis('off')\n",
    "    \n",
    "    plt.suptitle(f'{model_name} - Sample Predictions', fontsize=14, fontweight='bold')\n",
    "    plt.tight_layout()\n",
    "    plt.show()\n",
    "    \n",
    "    # Calculate accuracy on these samples\n",
    "    all_predictions, _ = classifier.predict(X_test[indices])\n",
    "    sample_accuracy = accuracy_score(y_test[indices], all_predictions)\n",
    "    print(f\"   Sample accuracy: {sample_accuracy:.4f} ({sample_accuracy*100:.2f}%)\")\n",
    "\n",
    "print(\"✅ Testing function defined!\")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Test Model 1 predictions\n",
    "if 'model1_classifier' in locals() and model1_classifier.svm_model is not None:\n",
    "    test_single_prediction(model1_classifier, X1_test, y1_test, \"Model 1 (Normal vs Dyslexia)\", num_samples=5)"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Test Model 2 predictions\n",
    "if 'model2_classifier' in locals() and model2_classifier.svm_model is not None:\n",
    "    test_single_prediction(model2_classifier, X2_test, y2_test, \"Model 2 (A-Z Letters)\", num_samples=5)"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 8. Summary and Results\n",
    "\n",
    "Let's summarize the training results and performance."
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Training Summary\n",
    "print(\"📋 TRAINING SUMMARY\")\n",
    "print(\"=\" * 50)\n",
    "\n",
    "results = {}\n",
    "\n",
    "if 'accuracy1' in locals():\n",
    "    results['Model 1 (Normal vs Dyslexia)'] = accuracy1\n",
    "    print(f\"🎯 Model 1 (Normal vs Dyslexia): {accuracy1:.4f} ({accuracy1*100:.2f}%)\")\n",
    "\n",
    "if 'accuracy2' in locals():\n",
    "    results['Model 2 (A-Z Letters)'] = accuracy2\n",
    "    print(f\"🎯 Model 2 (A-Z Letters): {accuracy2:.4f} ({accuracy2*100:.2f}%)\")\n",
    "\n",
    "if results:\n",
    "    print(f\"\\n📊 Average Accuracy: {np.mean(list(results.values())):.4f}\")\n",
    "    \n",
    "    # Plot results\n",
    "    if len(results) > 1:\n",
    "        plt.figure(figsize=(10, 6))\n",
    "        models = list(results.keys())\n",
    "        accuracies = list(results.values())\n",
    "        \n",
    "        bars = plt.bar(models, accuracies, color=['skyblue', 'lightcoral'])\n",
    "        plt.title('Model Performance Comparison', fontsize=16, fontweight='bold')\n",
    "        plt.ylabel('Accuracy')\n",
    "        plt.ylim(0, 1)\n",
    "        \n",
    "        # Add value labels on bars\n",
    "        for bar, acc in zip(bars, accuracies):\n",
    "            plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01, \n",
    "                    f'{acc:.3f}', ha='center', va='bottom', fontweight='bold')\n",
    "        \n",
    "        plt.tight_layout()\n",
    "        plt.show()\n",
    "else:\n",
    "    print(\"❌ No models were trained successfully.\")\n",
    "\n",
    "print(\"\\n🎉 Training completed!\")\n",
    "print(\"\\n📝 Next Steps:\")\n",
    "print(\"   1. Use the trained models for inference on new images\")\n",
    "print(\"   2. Fine-tune hyperparameters for better performance\")\n",
    "print(\"   3. Collect more data to improve accuracy\")\n",
    "print(\"   4. Try different CNN architectures (ResNet50, Custom)\")"
   ]
  }
 ],
 "metadata": {
  "kernelspec": {
   "display_name": "Python 3",
   "language": "python",
   "name": "python3"
  },
  "language_info": {
   "codemirror_mode": {
    "name": "ipython",
    "version": 3
   },
   "file_extension": ".py",
   "name": "python",
   "nbconvert_exporter": "python",
   "pygments_lexer": "ipython3",
   "version": "3.8.5"
  }
 },
 "nbformat": 4,
 "nbformat_minor": 4
}
