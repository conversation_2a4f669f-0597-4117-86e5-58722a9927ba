import os
import cv2
import numpy as np
import shutil
from tqdm import tqdm

# Path to your original dataset (normal handwritten)
normal_dir = 'dataset/normal'

# Output folder
output_dir = 'binary_dataset'
normal_out = os.path.join(output_dir, 'normal')
dyslexia_out = os.path.join(output_dir, 'dyslexia')

def prepare_dirs():
    # Delete old binary_dataset if it exists
    if os.path.exists(output_dir):
        shutil.rmtree(output_dir)
    os.makedirs(normal_out)
    os.makedirs(dyslexia_out)

def process_images():
    for label_folder in os.listdir(normal_dir):
        label_path = os.path.join(normal_dir, label_folder)
        if not os.path.isdir(label_path):
            continue

        for filename in tqdm(os.listdir(label_path), desc=f"Processing {label_folder}"):
            img_path = os.path.join(label_path, filename)
            img = cv2.imread(img_path)
            if img is None:
                continue

            # ✅ Convert to grayscale
            gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)

            # ✅ Save as normal
            norm_save_path = os.path.join(normal_out, f"{label_folder}_{filename}")
            cv2.imwrite(norm_save_path, gray)

            # ✅ Create dyslexia-style version
            flipped = cv2.flip(gray, 1)  # horizontal flip

            # Slight random rotation
            angle = np.random.uniform(-20, 20)
            h, w = gray.shape
            M = cv2.getRotationMatrix2D((w // 2, h // 2), angle, 1)
            rotated = cv2.warpAffine(flipped, M, (w, h))

            # Add slight blur
            blurred = cv2.GaussianBlur(rotated, (3, 3), 0)

            # ✅ Save as dyslexia version
            dys_save_path = os.path.join(dyslexia_out, f"{label_folder}_dys_{filename}")
            cv2.imwrite(dys_save_path, blurred)

# Run the steps
prepare_dirs()
process_images()
print("✅ Dataset generated successfully under 'binary_dataset/'")
