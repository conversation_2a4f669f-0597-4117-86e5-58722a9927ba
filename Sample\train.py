import tensorflow as tf
from tensorflow.keras.preprocessing.image import ImageDataGenerator
from tensorflow.keras.applications import MobileNetV2
from tensorflow.keras.layers import Dense, GlobalAveragePooling2D, Input, Conv2D
from tensorflow.keras.models import Model
from tensorflow.keras.optimizers import Adam
import os

# Settings
img_size = 224
batch_size = 32
base_dir = 'final_dataset'

# Load grayscale images
datagen = ImageDataGenerator(rescale=1./255)

train_gen = datagen.flow_from_directory(
    os.path.join(base_dir, 'train'),
    target_size=(img_size, img_size),
    color_mode='grayscale',
    batch_size=batch_size,
    class_mode='binary'
)

val_gen = datagen.flow_from_directory(
    os.path.join(base_dir, 'val'),
    target_size=(img_size, img_size),
    color_mode='grayscale',
    batch_size=batch_size,
    class_mode='binary'
)

# ✅ Input for grayscale image
input_layer = Input(shape=(img_size, img_size, 1))

# ✅ Convert grayscale (1 channel) to 3 channels
x = Conv2D(3, (3, 3), padding='same', name='grayscale_to_rgb')(input_layer)

# ✅ Use MobileNetV2 with standard input_shape and weights
mobilenet = MobileNetV2(weights='imagenet', include_top=False, input_shape=(img_size, img_size, 3))
mobilenet.trainable = False  # freeze pretrained layers

# Pass the converted input to the mobilenet
x = mobilenet(x)
x = GlobalAveragePooling2D()(x)
output = Dense(1, activation='sigmoid')(x)

model = Model(inputs=input_layer, outputs=output)

# Compile and train
model.compile(optimizer=Adam(learning_rate=0.0001),
              loss='binary_crossentropy',
              metrics=['accuracy'])

model.fit(train_gen, epochs=10, validation_data=val_gen)

model.save('dyslexia_model_mobilenetv2.h5')
print("✅ Training complete and model saved.")
