{"cells": [{"cell_type": "code", "execution_count": 1, "id": "0f8f38e5", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["\n", "A module that was compiled using NumPy 1.x cannot be run in\n", "NumPy 2.2.5 as it may crash. To support both 1.x and 2.x\n", "versions of NumPy, modules must be compiled with NumPy 2.0.\n", "Some module may need to rebuild instead e.g. with 'pybind11>=2.12'.\n", "\n", "If you are a user of the module, the easiest solution will be to\n", "downgrade to 'numpy<2' or try to upgrade the affected module.\n", "We expect that some modules will need time to support NumPy 2.\n", "\n", "Traceback (most recent call last):  File \"<frozen runpy>\", line 198, in _run_module_as_main\n", "  File \"<frozen runpy>\", line 88, in _run_code\n", "  File \"C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\ipykernel_launcher.py\", line 18, in <module>\n", "    app.launch_new_instance()\n", "  File \"C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\traitlets\\config\\application.py\", line 1075, in launch_instance\n", "    app.start()\n", "  File \"C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\ipykernel\\kernelapp.py\", line 739, in start\n", "    self.io_loop.start()\n", "  File \"C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\tornado\\platform\\asyncio.py\", line 211, in start\n", "    self.asyncio_loop.run_forever()\n", "  File \"C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\asyncio\\base_events.py\", line 608, in run_forever\n", "    self._run_once()\n", "  File \"C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\asyncio\\base_events.py\", line 1936, in _run_once\n", "    handle._run()\n", "  File \"C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\asyncio\\events.py\", line 84, in _run\n", "    self._context.run(self._callback, *self._args)\n", "  File \"C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\ipykernel\\kernelbase.py\", line 545, in dispatch_queue\n", "    await self.process_one()\n", "  File \"C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\ipykernel\\kernelbase.py\", line 534, in process_one\n", "    await dispatch(*args)\n", "  File \"C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\ipykernel\\kernelbase.py\", line 437, in dispatch_shell\n", "    await result\n", "  File \"C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\ipykernel\\ipkernel.py\", line 362, in execute_request\n", "    await super().execute_request(stream, ident, parent)\n", "  File \"C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\ipykernel\\kernelbase.py\", line 778, in execute_request\n", "    reply_content = await reply_content\n", "  File \"C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\ipykernel\\ipkernel.py\", line 449, in do_execute\n", "    res = shell.run_cell(\n", "  File \"C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\ipykernel\\zmqshell.py\", line 549, in run_cell\n", "    return super().run_cell(*args, **kwargs)\n", "  File \"C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\IPython\\core\\interactiveshell.py\", line 3116, in run_cell\n", "    result = self._run_cell(\n", "  File \"C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\IPython\\core\\interactiveshell.py\", line 3171, in _run_cell\n", "    result = runner(coro)\n", "  File \"C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\IPython\\core\\async_helpers.py\", line 128, in _pseudo_sync_runner\n", "    coro.send(None)\n", "  File \"C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\IPython\\core\\interactiveshell.py\", line 3394, in run_cell_async\n", "    has_raised = await self.run_ast_nodes(code_ast.body, cell_name,\n", "  File \"C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\IPython\\core\\interactiveshell.py\", line 3639, in run_ast_nodes\n", "    if await self.run_code(code, result, async_=asy):\n", "  File \"C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\IPython\\core\\interactiveshell.py\", line 3699, in run_code\n", "    exec(code_obj, self.user_global_ns, self.user_ns)\n", "  File \"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_8460\\380641784.py\", line 2, in <module>\n", "    import cv2\n", "  File \"C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\cv2\\__init__.py\", line 181, in <module>\n", "    bootstrap()\n", "  File \"C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\cv2\\__init__.py\", line 153, in bootstrap\n", "    native_module = importlib.import_module(\"cv2\")\n", "  File \"C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\importlib\\__init__.py\", line 126, in import_module\n", "    return _bootstrap._gcd_import(name[level:], package, level)\n"]}, {"ename": "AttributeError", "evalue": "_ARRAY_API not found", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mAttributeError\u001b[39m                            <PERSON><PERSON> (most recent call last)", "\u001b[31mAttributeError\u001b[39m: _ARRAY_API not found"]}, {"ename": "ImportError", "evalue": "numpy.core.multiarray failed to import", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mImportError\u001b[39m                               Trace<PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[1]\u001b[39m\u001b[32m, line 2\u001b[39m\n\u001b[32m      1\u001b[39m \u001b[38;5;28;01mimport\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mos\u001b[39;00m\n\u001b[32m----> \u001b[39m\u001b[32m2\u001b[39m \u001b[38;5;28;01mimport\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mcv2\u001b[39;00m\n\u001b[32m      3\u001b[39m \u001b[38;5;28;01mimport\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mshutil\u001b[39;00m\n\u001b[32m      5\u001b[39m \u001b[38;5;66;03m# Set your dataset path\u001b[39;00m\n", "\u001b[36mFile \u001b[39m\u001b[32m~\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\cv2\\__init__.py:181\u001b[39m\n\u001b[32m    176\u001b[39m             \u001b[38;5;28;01mif\u001b[39;00m DEBUG: \u001b[38;5;28mprint\u001b[39m(\u001b[33m\"\u001b[39m\u001b[33mExtra Python code for\u001b[39m\u001b[33m\"\u001b[39m, submodule, \u001b[33m\"\u001b[39m\u001b[33mis loaded\u001b[39m\u001b[33m\"\u001b[39m)\n\u001b[32m    178\u001b[39m     \u001b[38;5;28;01mif\u001b[39;00m DEBUG: \u001b[38;5;28mprint\u001b[39m(\u001b[33m'\u001b[39m\u001b[33mOpenCV loader: DONE\u001b[39m\u001b[33m'\u001b[39m)\n\u001b[32m--> \u001b[39m\u001b[32m181\u001b[39m \u001b[43mbootstrap\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\cv2\\__init__.py:153\u001b[39m, in \u001b[36mbootstrap\u001b[39m\u001b[34m()\u001b[39m\n\u001b[32m    149\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m DEBUG: \u001b[38;5;28mprint\u001b[39m(\u001b[33m\"\u001b[39m\u001b[33mRelink everything from native cv2 module to cv2 package\u001b[39m\u001b[33m\"\u001b[39m)\n\u001b[32m    151\u001b[39m py_module = sys.modules.pop(\u001b[33m\"\u001b[39m\u001b[33mcv2\u001b[39m\u001b[33m\"\u001b[39m)\n\u001b[32m--> \u001b[39m\u001b[32m153\u001b[39m native_module = \u001b[43mimportlib\u001b[49m\u001b[43m.\u001b[49m\u001b[43mimport_module\u001b[49m\u001b[43m(\u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mcv2\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m)\u001b[49m\n\u001b[32m    155\u001b[39m sys.modules[\u001b[33m\"\u001b[39m\u001b[33mcv2\u001b[39m\u001b[33m\"\u001b[39m] = py_module\n\u001b[32m    156\u001b[39m \u001b[38;5;28msetattr\u001b[39m(py_module, \u001b[33m\"\u001b[39m\u001b[33m_native\u001b[39m\u001b[33m\"\u001b[39m, native_module)\n", "\u001b[36mFile \u001b[39m\u001b[32mC:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\importlib\\__init__.py:126\u001b[39m, in \u001b[36mimport_module\u001b[39m\u001b[34m(name, package)\u001b[39m\n\u001b[32m    124\u001b[39m             \u001b[38;5;28;01mbreak\u001b[39;00m\n\u001b[32m    125\u001b[39m         level += \u001b[32m1\u001b[39m\n\u001b[32m--> \u001b[39m\u001b[32m126\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43m_bootstrap\u001b[49m\u001b[43m.\u001b[49m\u001b[43m_gcd_import\u001b[49m\u001b[43m(\u001b[49m\u001b[43mname\u001b[49m\u001b[43m[\u001b[49m\u001b[43mlevel\u001b[49m\u001b[43m:\u001b[49m\u001b[43m]\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mpackage\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mlevel\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[31mImportError\u001b[39m: numpy.core.multiarray failed to import"]}], "source": ["import os\n", "import cv2\n", "import shutil\n", "\n", "# Set your dataset path\n", "DATASET_PATH = \"dataset/normal\"\n", "CLEANED_PATH = \"dataset/cleaned_normal\"\n", "os.makedirs(CLEANED_PATH, exist_ok=True)\n", "\n", "# Loop through each label folder\n", "for label in os.listdir(DATASET_PATH):\n", "    label_path = os.path.join(DATASET_PATH, label)\n", "    if not os.path.isdir(label_path):\n", "        continue\n", "\n", "    # Create output folder for cleaned data\n", "    cleaned_label_path = os.path.join(CLEANED_PATH, label)\n", "    os.makedirs(cleaned_label_path, exist_ok=True)\n", "\n", "    for image_name in os.listdir(label_path):\n", "        image_path = os.path.join(label_path, image_name)\n", "        img = cv2.imread(image_path)\n", "\n", "        if img is None:\n", "            continue  # skip broken images\n", "\n", "        resized = cv2.resize(img, (200, 200))\n", "        cv2.imshow(f\"{label} - Press k=keep, d=delete\", resized)\n", "        key = cv2.<PERSON><PERSON><PERSON>(0)\n", "\n", "        if key == ord('k'):\n", "            shutil.copy(image_path, os.path.join(cleaned_label_path, image_name))\n", "        elif key == ord('d'):\n", "            print(f\"Deleted: {image_name}\")\n", "        elif key == 27:  # E<PERSON> to quit\n", "            break\n", "\n", "    cv2.destroyAllWindows()\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 5}