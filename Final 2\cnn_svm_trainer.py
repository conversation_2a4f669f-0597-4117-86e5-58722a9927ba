import os
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.svm import SVC
from sklearn.metrics import classification_report, confusion_matrix, accuracy_score
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.model_selection import train_test_split, GridSearchCV
import tensorflow as tf
from tensorflow.keras.models import Model, Sequential
from tensorflow.keras.layers import Conv2D, MaxPooling2D, Flatten, Dense, Dropout, GlobalAveragePooling2D
from tensorflow.keras.preprocessing.image import ImageDataGenerator, load_img, img_to_array
from tensorflow.keras.applications import VGG16, ResNet50
from tensorflow.keras.optimizers import Adam
from tensorflow.keras.callbacks import EarlyStopping, ReduceLROnPlateau
import cv2
from tqdm import tqdm
import warnings
warnings.filterwarnings('ignore')

class CNNSVMClassifier:
    def __init__(self, img_size=(224, 224), use_pretrained=True, model_type='VGG16'):
        """
        Initialize CNN-SVM Classifier
        
        Args:
            img_size: Input image size (height, width)
            use_pretrained: Whether to use pretrained CNN as feature extractor
            model_type: Type of pretrained model ('VGG16', 'ResNet50', or 'Custom')
        """
        self.img_size = img_size
        self.use_pretrained = use_pretrained
        self.model_type = model_type
        self.cnn_model = None
        self.svm_model = None
        self.scaler = StandardScaler()
        self.label_encoder = LabelEncoder()
        
    def build_cnn_feature_extractor(self, input_shape, num_classes=None):
        """Build CNN model for feature extraction"""
        if self.use_pretrained and self.model_type == 'VGG16':
            # Use VGG16 as feature extractor
            base_model = VGG16(weights='imagenet', include_top=False, input_shape=input_shape)
            base_model.trainable = False  # Freeze base model
            
            model = Sequential([
                base_model,
                GlobalAveragePooling2D(),
                Dense(512, activation='relu'),
                Dropout(0.5),
                Dense(256, activation='relu'),
                Dropout(0.3)
            ])
            
        elif self.use_pretrained and self.model_type == 'ResNet50':
            # Use ResNet50 as feature extractor
            base_model = ResNet50(weights='imagenet', include_top=False, input_shape=input_shape)
            base_model.trainable = False
            
            model = Sequential([
                base_model,
                GlobalAveragePooling2D(),
                Dense(512, activation='relu'),
                Dropout(0.5),
                Dense(256, activation='relu'),
                Dropout(0.3)
            ])
            
        else:
            # Custom CNN architecture
            model = Sequential([
                Conv2D(32, (3, 3), activation='relu', input_shape=input_shape),
                MaxPooling2D((2, 2)),
                Conv2D(64, (3, 3), activation='relu'),
                MaxPooling2D((2, 2)),
                Conv2D(128, (3, 3), activation='relu'),
                MaxPooling2D((2, 2)),
                Conv2D(256, (3, 3), activation='relu'),
                MaxPooling2D((2, 2)),
                Flatten(),
                Dense(512, activation='relu'),
                Dropout(0.5),
                Dense(256, activation='relu'),
                Dropout(0.3)
            ])
        
        return model
    
    def load_and_preprocess_data(self, data_path, dataset_type='model1'):
        """
        Load and preprocess image data
        
        Args:
            data_path: Path to dataset
            dataset_type: 'model1' for Normal/Dyslexia or 'model2' for A-Z letters
        """
        images = []
        labels = []
        
        if dataset_type == 'model1':
            # Binary classification: Normal vs Dyslexia
            train_path = os.path.join(data_path, 'Train')
            test_path = os.path.join(data_path, 'test')
            
            for split in ['Train', 'test']:
                split_path = os.path.join(data_path, split)
                if not os.path.exists(split_path):
                    continue
                    
                for class_name in ['Normal', 'Dyslexia']:
                    class_path = os.path.join(split_path, class_name)
                    if not os.path.exists(class_path):
                        continue
                    
                    # Process all letter subdirectories
                    for letter_dir in os.listdir(class_path):
                        letter_path = os.path.join(class_path, letter_dir)
                        if os.path.isdir(letter_path):
                            for img_file in tqdm(os.listdir(letter_path), 
                                               desc=f"Loading {split}/{class_name}/{letter_dir}"):
                                if img_file.lower().endswith(('.png', '.jpg', '.jpeg')):
                                    img_path = os.path.join(letter_path, img_file)
                                    try:
                                        img = self.preprocess_image(img_path)
                                        images.append(img)
                                        labels.append(class_name)
                                    except Exception as e:
                                        print(f"Error loading {img_path}: {e}")
                                        
        elif dataset_type == 'model2':
            # Multi-class classification: A-Z letters
            train_path = os.path.join(data_path, 'train')
            test_path = os.path.join(data_path, 'test')
            
            for split in ['train', 'test']:
                split_path = os.path.join(data_path, split)
                if not os.path.exists(split_path):
                    continue
                    
                for letter in 'ABCDEFGHIJKLMNOPQRSTUVWXYZ':
                    letter_path = os.path.join(split_path, letter)
                    if not os.path.exists(letter_path):
                        continue
                        
                    for img_file in tqdm(os.listdir(letter_path), 
                                       desc=f"Loading {split}/{letter}"):
                        if img_file.lower().endswith(('.png', '.jpg', '.jpeg')):
                            img_path = os.path.join(letter_path, img_file)
                            try:
                                img = self.preprocess_image(img_path)
                                images.append(img)
                                labels.append(letter)
                            except Exception as e:
                                print(f"Error loading {img_path}: {e}")
        
        return np.array(images), np.array(labels)
    
    def preprocess_image(self, img_path):
        """Preprocess individual image"""
        img = cv2.imread(img_path)
        if img is None:
            raise ValueError(f"Could not load image: {img_path}")
            
        img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
        img = cv2.resize(img, self.img_size)
        img = img.astype('float32') / 255.0
        return img
    
    def extract_features(self, images):
        """Extract features using CNN"""
        if self.cnn_model is None:
            raise ValueError("CNN model not built. Call build_and_train_cnn first.")
        
        features = self.cnn_model.predict(images, batch_size=32, verbose=1)
        return features
    
    def build_and_train_cnn(self, X_train, y_train, X_val=None, y_val=None, epochs=50):
        """Build and train CNN feature extractor"""
        input_shape = X_train.shape[1:]
        num_classes = len(np.unique(y_train))
        
        # Build CNN model
        self.cnn_model = self.build_cnn_feature_extractor(input_shape, num_classes)
        
        print("CNN Model Architecture:")
        self.cnn_model.summary()
        
        # Compile model
        self.cnn_model.compile(
            optimizer=Adam(learning_rate=0.001),
            loss='sparse_categorical_crossentropy',
            metrics=['accuracy']
        )
        
        # Encode labels
        y_train_encoded = self.label_encoder.fit_transform(y_train)
        if y_val is not None:
            y_val_encoded = self.label_encoder.transform(y_val)
        
        # Callbacks
        callbacks = [
            EarlyStopping(patience=10, restore_best_weights=True),
            ReduceLROnPlateau(factor=0.5, patience=5)
        ]
        
        # Train CNN
        if X_val is not None and y_val is not None:
            history = self.cnn_model.fit(
                X_train, y_train_encoded,
                validation_data=(X_val, y_val_encoded),
                epochs=epochs,
                batch_size=32,
                callbacks=callbacks,
                verbose=1
            )
        else:
            history = self.cnn_model.fit(
                X_train, y_train_encoded,
                epochs=epochs,
                batch_size=32,
                callbacks=callbacks,
                verbose=1
            )
        
        return history
    
    def train_svm(self, X_train, y_train, param_grid=None):
        """Train SVM classifier on CNN features"""
        if param_grid is None:
            param_grid = {
                'C': [0.1, 1, 10, 100],
                'kernel': ['rbf', 'linear'],
                'gamma': ['scale', 'auto', 0.001, 0.01, 0.1, 1]
            }
        
        # Extract features using CNN
        print("Extracting features for SVM training...")
        features = self.extract_features(X_train)
        
        # Scale features
        features_scaled = self.scaler.fit_transform(features)
        
        # Grid search for best SVM parameters
        print("Performing grid search for SVM hyperparameters...")
        svm = SVC(probability=True, random_state=42)
        grid_search = GridSearchCV(svm, param_grid, cv=5, scoring='accuracy', n_jobs=-1, verbose=1)
        grid_search.fit(features_scaled, y_train)
        
        self.svm_model = grid_search.best_estimator_
        
        print(f"Best SVM parameters: {grid_search.best_params_}")
        print(f"Best cross-validation score: {grid_search.best_score_:.4f}")
        
        return grid_search.best_score_
    
    def predict(self, X_test):
        """Make predictions using CNN-SVM pipeline"""
        # Extract features
        features = self.extract_features(X_test)
        
        # Scale features
        features_scaled = self.scaler.transform(features)
        
        # Predict using SVM
        predictions = self.svm_model.predict(features_scaled)
        probabilities = self.svm_model.predict_proba(features_scaled)
        
        return predictions, probabilities
    
    def evaluate(self, X_test, y_test):
        """Evaluate model performance"""
        predictions, probabilities = self.predict(X_test)
        
        # Calculate metrics
        accuracy = accuracy_score(y_test, predictions)
        report = classification_report(y_test, predictions)
        cm = confusion_matrix(y_test, predictions)
        
        print(f"Test Accuracy: {accuracy:.4f}")
        print("\nClassification Report:")
        print(report)
        
        # Plot confusion matrix
        plt.figure(figsize=(10, 8))
        sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', 
                   xticklabels=self.label_encoder.classes_,
                   yticklabels=self.label_encoder.classes_)
        plt.title('Confusion Matrix')
        plt.ylabel('True Label')
        plt.xlabel('Predicted Label')
        plt.tight_layout()
        plt.show()
        
        return accuracy, report, cm
    
    def save_model(self, filepath):
        """Save trained models"""
        import joblib
        
        # Save CNN model
        self.cnn_model.save(f"{filepath}_cnn.h5")
        
        # Save SVM model and scaler
        joblib.dump(self.svm_model, f"{filepath}_svm.pkl")
        joblib.dump(self.scaler, f"{filepath}_scaler.pkl")
        joblib.dump(self.label_encoder, f"{filepath}_label_encoder.pkl")
        
        print(f"Models saved with prefix: {filepath}")
    
    def load_model(self, filepath):
        """Load trained models"""
        import joblib
        from tensorflow.keras.models import load_model
        
        # Load CNN model
        self.cnn_model = load_model(f"{filepath}_cnn.h5")
        
        # Load SVM model and scaler
        self.svm_model = joblib.load(f"{filepath}_svm.pkl")
        self.scaler = joblib.load(f"{filepath}_scaler.pkl")
        self.label_encoder = joblib.load(f"{filepath}_label_encoder.pkl")
        
        print(f"Models loaded from: {filepath}")
