from flask import Flask, request, render_template_string, jsonify
import tensorflow as tf
import numpy as np
from PIL import Image, ImageEnhance, ImageFilter, ImageOps
import os
import base64
from io import BytesIO
import cv2

app = Flask(__name__)

# Global variable to store the model
model = None

def load_model():
    """Load the trained dyslexia detection model"""
    global model
    try:
        model = tf.keras.models.load_model('dyslexia_model_mobilenetv2.h5')
        print("✅ Model loaded successfully!")
        return True
    except Exception as e:
        print(f"❌ Error loading model: {e}")
        return False

def remove_background(image):
    """Remove background and enhance handwriting visibility"""
    try:
        # Convert PIL to OpenCV format
        img_array = np.array(image)

        # If image is RGB, convert to grayscale
        if len(img_array.shape) == 3:
            gray = cv2.cvtColor(img_array, cv2.COLOR_RGB2GRAY)
        else:
            gray = img_array

        # Apply Gaussian blur to reduce noise
        blurred = cv2.GaussianBlur(gray, (5, 5), 0)

        # Apply adaptive thresholding to separate text from background
        # This works better than simple thresholding for varying lighting
        thresh = cv2.adaptiveThreshold(
            blurred, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
            cv2.THRESH_BINARY, 11, 2
        )

        # Invert the image so text is black on white background
        thresh = cv2.bitwise_not(thresh)

        # Apply morphological operations to clean up the image
        kernel = np.ones((2, 2), np.uint8)
        cleaned = cv2.morphologyEx(thresh, cv2.MORPH_CLOSE, kernel)
        cleaned = cv2.morphologyEx(cleaned, cv2.MORPH_OPEN, kernel)

        # Convert back to PIL Image
        processed_img = Image.fromarray(cleaned)

        return processed_img

    except Exception as e:
        print(f"❌ Error removing background: {e}")
        # Return original image if background removal fails
        return image.convert('L')

def enhance_contrast(image):
    """Enhance contrast to make handwriting more visible"""
    try:
        # Convert to numpy array
        img_array = np.array(image)

        # Apply CLAHE (Contrast Limited Adaptive Histogram Equalization)
        clahe = cv2.createCLAHE(clipLimit=3.0, tileGridSize=(8, 8))
        enhanced = clahe.apply(img_array)

        # Convert back to PIL
        enhanced_img = Image.fromarray(enhanced)

        # Additional contrast enhancement using PIL
        enhancer = ImageEnhance.Contrast(enhanced_img)
        enhanced_img = enhancer.enhance(1.5)  # Increase contrast by 50%

        return enhanced_img

    except Exception as e:
        print(f"❌ Error enhancing contrast: {e}")
        return image

def preprocess_image(image, img_size=224, remove_bg=True, enhance_contrast_flag=True):
    """Preprocess the input image with optional background removal and contrast enhancement"""
    try:
        # Convert to grayscale first
        img = image.convert('L')

        # Apply background removal if requested
        if remove_bg:
            img = remove_background(img)

        # Apply contrast enhancement if requested
        if enhance_contrast_flag:
            img = enhance_contrast(img)

        # Resize to match training size
        img = img.resize((img_size, img_size))

        # Convert to numpy array
        img_array = np.array(img)

        # Normalize to [0,1] range
        img_array = img_array.astype('float32') / 255.0

        # Add channel dimension (for grayscale)
        img_array = np.expand_dims(img_array, axis=-1)

        # Add batch dimension
        img_array = np.expand_dims(img_array, axis=0)

        return img_array, img  # Return both processed array and PIL image for display

    except Exception as e:
        print(f"❌ Error preprocessing image: {e}")
        return None, None

def predict_dyslexia(image_array):
    """Make prediction on the preprocessed image"""
    try:
        # Make prediction
        prediction = model.predict(image_array, verbose=0)

        # Get probability (sigmoid output) and convert to Python float
        probability = float(prediction[0][0])

        # Classification based on threshold 0.5
        if probability > 0.5:
            classification = "Normal"
            confidence = float(probability * 100)
        else:
            classification = "Dyslexia"
            confidence = float((1 - probability) * 100)

        return probability, classification, confidence

    except Exception as e:
        print(f"❌ Error making prediction: {e}")
        return None, None, None

# HTML template for the web interface
HTML_TEMPLATE = """
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dyslexia Detection</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .upload-area {
            border: 2px dashed #ccc;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            margin-bottom: 20px;
            background-color: #fafafa;
        }
        .upload-area:hover {
            border-color: #007bff;
            background-color: #f0f8ff;
        }
        input[type="file"] {
            margin: 20px 0;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .result {
            margin-top: 30px;
            padding: 20px;
            border-radius: 5px;
            display: none;
        }
        .result.dyslexia {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }
        .result.normal {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .preview {
            max-width: 300px;
            max-height: 300px;
            margin: 10px;
            display: inline-block;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .preview-container {
            text-align: center;
            margin: 20px 0;
        }
        .preview-section {
            display: inline-block;
            margin: 10px;
            vertical-align: top;
        }
        .preview-section h4 {
            margin: 5px 0;
            color: #666;
        }
        .options {
            text-align: left;
            margin: 15px 0;
        }
        .options label {
            display: block;
            margin: 8px 0;
            cursor: pointer;
        }
        .options input[type="checkbox"] {
            margin-right: 8px;
        }
        .disclaimer {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
            padding: 15px;
            border-radius: 5px;
            margin-top: 20px;
            font-size: 14px;
        }
        .loading {
            display: none;
            text-align: center;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧠 Dyslexia Detection from Handwriting</h1>
        
        <div class="upload-area">
            <h3>Upload a handwriting image</h3>
            <p>Select an image file (JPG, PNG, etc.) containing handwritten text</p>
            <input type="file" id="imageInput" accept="image/*">
            <br><br>

            <div class="options">
                <h4>Processing Options:</h4>
                <label>
                    <input type="checkbox" id="removeBg" checked> Remove background & enhance text
                </label>
                <br>
                <label>
                    <input type="checkbox" id="enhanceContrast" checked> Enhance contrast
                </label>
            </div>
            <br>
            <button onclick="analyzeImage()">Analyze Handwriting</button>
        </div>
        
        <div class="loading" id="loading">
            <p>🔄 Analyzing image... Please wait.</p>
        </div>
        
        <div id="preview" class="preview-container"></div>
        
        <div id="result" class="result">
            <h3>📊 Analysis Result</h3>
            <div id="resultContent"></div>
        </div>
        
        <div class="disclaimer">
            <strong>⚠️ Important Disclaimer:</strong><br>
            This is an AI model for research and educational purposes only. 
            It should not be used for medical diagnosis or professional assessment. 
            For proper dyslexia evaluation, please consult qualified healthcare professionals 
            or educational specialists.
        </div>
    </div>

    <script>
        function analyzeImage() {
            const input = document.getElementById('imageInput');
            const file = input.files[0];

            if (!file) {
                alert('Please select an image file first.');
                return;
            }

            // Get processing options
            const removeBg = document.getElementById('removeBg').checked;
            const enhanceContrast = document.getElementById('enhanceContrast').checked;

            // Show loading
            document.getElementById('loading').style.display = 'block';
            document.getElementById('result').style.display = 'none';

            // Show original preview
            const reader = new FileReader();
            reader.onload = function(e) {
                document.getElementById('preview').innerHTML =
                    '<div class="preview-section">' +
                    '<h4>Original Image</h4>' +
                    '<img src="' + e.target.result + '" class="preview" alt="Original image">' +
                    '</div>' +
                    '<div class="preview-section" id="processedPreview">' +
                    '<h4>Processing...</h4>' +
                    '</div>';
            };
            reader.readAsDataURL(file);

            // Send to server
            const formData = new FormData();
            formData.append('image', file);
            formData.append('remove_bg', removeBg);
            formData.append('enhance_contrast', enhanceContrast);

            fetch('/predict', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                document.getElementById('loading').style.display = 'none';

                if (data.error) {
                    alert('Error: ' + data.error);
                    return;
                }

                // Show processed image if available
                if (data.processed_image) {
                    document.getElementById('processedPreview').innerHTML =
                        '<h4>Processed Image</h4>' +
                        '<img src="data:image/png;base64,' + data.processed_image + '" class="preview" alt="Processed image">';
                }

                // Show result
                const resultDiv = document.getElementById('result');
                const resultContent = document.getElementById('resultContent');

                resultContent.innerHTML = `
                    <p><strong>Classification:</strong> ${data.classification}</p>
                    <p><strong>Confidence:</strong> ${data.confidence.toFixed(2)}%</p>
                    <p><strong>Raw Probability:</strong> ${data.probability.toFixed(4)}</p>
                    <hr>
                    <p><strong>Processing Applied:</strong></p>
                    <p>• Background removal: ${data.processing_info.background_removed ? 'Yes' : 'No'}</p>
                    <p>• Contrast enhancement: ${data.processing_info.contrast_enhanced ? 'Yes' : 'No'}</p>
                    <hr>
                    <p><strong>Interpretation:</strong></p>
                    <p>${data.interpretation}</p>
                `;

                resultDiv.className = 'result ' + (data.classification.toLowerCase() === 'dyslexia' ? 'dyslexia' : 'normal');
                resultDiv.style.display = 'block';
            })
            .catch(error => {
                document.getElementById('loading').style.display = 'none';
                alert('Error analyzing image: ' + error);
            });
        }
    </script>
</body>
</html>
"""

@app.route('/')
def index():
    return render_template_string(HTML_TEMPLATE)

@app.route('/predict', methods=['POST'])
def predict():
    try:
        if 'image' not in request.files:
            return jsonify({'error': 'No image file provided'})

        file = request.files['image']
        if file.filename == '':
            return jsonify({'error': 'No image file selected'})

        # Get processing options
        remove_bg = request.form.get('remove_bg', 'true').lower() == 'true'
        enhance_contrast_flag = request.form.get('enhance_contrast', 'true').lower() == 'true'

        # Load image
        image = Image.open(file.stream)

        # Preprocess image with options
        image_array, processed_image = preprocess_image(
            image,
            remove_bg=remove_bg,
            enhance_contrast_flag=enhance_contrast_flag
        )

        if image_array is None:
            return jsonify({'error': 'Failed to preprocess image'})

        # Convert processed image to base64 for display
        processed_image_b64 = None
        if processed_image is not None:
            buffer = BytesIO()
            processed_image.save(buffer, format='PNG')
            processed_image_b64 = base64.b64encode(buffer.getvalue()).decode()

        # Make prediction
        probability, classification, confidence = predict_dyslexia(image_array)
        if classification is None:
            return jsonify({'error': 'Failed to make prediction'})

        # Prepare interpretation
        if classification == "Dyslexia":
            interpretation = "The handwriting shows patterns that may be consistent with dyslexia. Consider further assessment by a qualified professional."
        else:
            interpretation = "The handwriting appears to follow typical patterns. No strong indicators of dyslexia detected."

        return jsonify({
            'classification': classification,
            'confidence': float(confidence),
            'probability': float(probability),
            'interpretation': interpretation,
            'processed_image': processed_image_b64,
            'processing_info': {
                'background_removed': remove_bg,
                'contrast_enhanced': enhance_contrast_flag
            }
        })

    except Exception as e:
        return jsonify({'error': str(e)})

if __name__ == '__main__':
    print("🧠 Starting Dyslexia Detection Web Interface...")
    
    # Load model
    if load_model():
        print("🌐 Starting web server...")
        print("📱 Open your browser and go to: http://localhost:5000")
        app.run(debug=True, host='0.0.0.0', port=5000)
    else:
        print("❌ Failed to load model. Please check if 'dyslexia_model_mobilenetv2.h5' exists.")
