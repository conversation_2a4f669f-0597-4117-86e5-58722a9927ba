# CNN-SVM Handwriting Analysis

This project implements a hybrid CNN-SVM approach for handwriting analysis with two main applications:

1. **Model 1**: Binary classification between Normal and Dyslexia handwriting
2. **Model 2**: Multi-class classification of alphabet letters (A-Z)

## Architecture

The system uses a two-stage approach:
1. **CNN Feature Extractor**: Uses pre-trained VGG16/ResNet50 or custom CNN to extract high-level features
2. **SVM Classifier**: Uses extracted features for final classification with optimized hyperparameters

## Features

- **Hybrid CNN-SVM Architecture**: Combines deep learning feature extraction with SVM classification
- **Pre-trained Models**: Supports VGG16, ResNet50, or custom CNN architectures
- **Automatic Hyperparameter Tuning**: Grid search for optimal SVM parameters
- **Comprehensive Evaluation**: Detailed metrics, confusion matrices, and visualizations
- **Batch Processing**: Support for single image and batch predictions
- **Model Persistence**: Save and load trained models

## Installation

1. Clone or download the project files
2. Install required dependencies:

```bash
pip install -r requirements.txt
```

## Dataset Structure

Your datasets should be organized as follows:

### Model 1 (Normal vs Dyslexia)
```
Model 1 Normal and dyslexia/
├── Train/
│   ├── Normal/
│   │   ├── A/
│   │   │   ├── image1.jpg
│   │   │   └── image2.jpg
│   │   ├── B/
│   │   └── ...
│   └── Dyslexia/
│       ├── A/
│       ├── B/
│       └── ...
└── test/
    ├── Normal/
    └── Dyslexia/
```

### Model 2 (A-Z Letters)
```
Model 2 Alphabets 26 letters/
├── train/
│   ├── A/
│   │   ├── image1.jpg
│   │   └── image2.jpg
│   ├── B/
│   └── ...
└── test/
    ├── A/
    ├── B/
    └── ...
```

## Usage

### Training Models

Train both models:
```bash
python train_models.py --model both --epochs 30
```

Train only Model 1 (Normal vs Dyslexia):
```bash
python train_models.py --model model1 --epochs 30
```

Train only Model 2 (A-Z Letters):
```bash
python train_models.py --model model2 --epochs 30
```

### Making Predictions

#### Single Image Prediction

For Normal vs Dyslexia classification:
```bash
python inference.py --model_path model1_normal_dyslexia --image_path path/to/image.jpg --model_type model1 --visualize
```

For alphabet letter classification:
```bash
python inference.py --model_path model2_alphabet_letters --image_path path/to/image.jpg --model_type model2 --visualize
```

#### Batch Prediction

Process multiple images in a directory:
```bash
python inference.py --model_path model1_normal_dyslexia --images_dir path/to/images/ --model_type model1
```

## Model Configuration

You can customize the CNN architecture in `cnn_svm_trainer.py`:

```python
classifier = CNNSVMClassifier(
    img_size=(224, 224),        # Input image size
    use_pretrained=True,        # Use pre-trained weights
    model_type='VGG16'          # 'VGG16', 'ResNet50', or 'Custom'
)
```

## Training Process

1. **Data Loading**: Images are loaded and preprocessed (resized, normalized)
2. **CNN Training**: Feature extractor is trained with early stopping and learning rate reduction
3. **Feature Extraction**: CNN extracts features from training images
4. **SVM Training**: Grid search finds optimal SVM hyperparameters
5. **Evaluation**: Model performance is evaluated on test set
6. **Model Saving**: Trained models are saved for future use

## Output Files

After training, the following files are generated:

- `model1_normal_dyslexia_cnn.h5`: CNN model for Model 1
- `model1_normal_dyslexia_svm.pkl`: SVM classifier for Model 1
- `model1_normal_dyslexia_scaler.pkl`: Feature scaler for Model 1
- `model1_normal_dyslexia_label_encoder.pkl`: Label encoder for Model 1
- `model2_alphabet_letters_*`: Similar files for Model 2
- Training history plots and confusion matrices

## Performance Metrics

The system provides comprehensive evaluation metrics:

- **Accuracy**: Overall classification accuracy
- **Precision, Recall, F1-Score**: Per-class and macro-averaged metrics
- **Confusion Matrix**: Visual representation of classification results
- **Training History**: Loss and accuracy curves during CNN training

## Customization

### Custom CNN Architecture

Modify the `build_cnn_feature_extractor` method in `CNNSVMClassifier`:

```python
def build_cnn_feature_extractor(self, input_shape, num_classes=None):
    model = Sequential([
        Conv2D(64, (3, 3), activation='relu', input_shape=input_shape),
        MaxPooling2D((2, 2)),
        # Add more layers...
        Flatten(),
        Dense(512, activation='relu'),
        Dropout(0.5)
    ])
    return model
```

### SVM Hyperparameters

Customize the parameter grid in the `train_svm` method:

```python
param_grid = {
    'C': [0.1, 1, 10, 100],
    'kernel': ['rbf', 'linear', 'poly'],
    'gamma': ['scale', 'auto', 0.001, 0.01]
}
```

## Troubleshooting

### Common Issues

1. **Memory Errors**: Reduce batch size or image size if you encounter memory issues
2. **Long Training Time**: Use smaller parameter grids or fewer epochs
3. **Poor Performance**: Try different CNN architectures or increase training data

### GPU Support

To use GPU acceleration (recommended):
```bash
pip install tensorflow-gpu
```

Ensure CUDA and cuDNN are properly installed.

## Example Results

Typical performance metrics:

- **Model 1 (Normal vs Dyslexia)**: 85-95% accuracy
- **Model 2 (A-Z Letters)**: 80-90% accuracy

Results depend on dataset quality, size, and training parameters.

## Contributing

Feel free to contribute improvements:
- Add new CNN architectures
- Implement data augmentation
- Add more evaluation metrics
- Optimize hyperparameter search

## License

This project is open source and available under the MIT License.
