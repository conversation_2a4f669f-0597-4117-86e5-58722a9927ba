#!/usr/bin/env python3
"""
Quick Dyslexia Detection Test Script

This script trains and tests a CNN-SVM model specifically for detecting
dyslexia vs normal handwriting from your dataset.
"""

import os
import numpy as np
import matplotlib.pyplot as plt
from sklearn.metrics import accuracy_score, classification_report, confusion_matrix
from sklearn.model_selection import train_test_split
import time

# Import our CNN-SVM classifier
from cnn_svm_trainer import CNNSVMClassifier

def check_dyslexia_dataset():
    """Check if dyslexia dataset is available"""
    dataset_path = "Model 1 Normal and dyslexia"
    
    if not os.path.exists(dataset_path):
        print(f"❌ Dataset not found: {dataset_path}")
        return False, dataset_path
    
    # Count samples
    train_path = os.path.join(dataset_path, "Train")
    normal_count = 0
    dyslexia_count = 0
    
    # Count Normal samples
    normal_train_path = os.path.join(train_path, "Normal")
    if os.path.exists(normal_train_path):
        for letter_dir in os.listdir(normal_train_path):
            letter_path = os.path.join(normal_train_path, letter_dir)
            if os.path.isdir(letter_path):
                normal_count += len([f for f in os.listdir(letter_path) 
                                   if f.lower().endswith(('.png', '.jpg', '.jpeg'))])
    
    # Count Dyslexia samples
    dyslexia_train_path = os.path.join(train_path, "Dyslexia")
    if os.path.exists(dyslexia_train_path):
        for letter_dir in os.listdir(dyslexia_train_path):
            letter_path = os.path.join(dyslexia_train_path, letter_dir)
            if os.path.isdir(letter_path):
                dyslexia_count += len([f for f in os.listdir(letter_path) 
                                     if f.lower().endswith(('.png', '.jpg', '.jpeg'))])
    
    print(f"✅ Found dataset: {dataset_path}")
    print(f"   Normal handwriting samples: {normal_count}")
    print(f"   Dyslexia handwriting samples: {dyslexia_count}")
    print(f"   Total samples: {normal_count + dyslexia_count}")
    
    if normal_count > 0 and dyslexia_count > 0:
        return True, dataset_path
    else:
        print("❌ Dataset appears to be empty or incorrectly structured")
        return False, dataset_path

def train_dyslexia_detector():
    """Train CNN-SVM model for dyslexia detection"""
    print("🧠 DYSLEXIA DETECTION TRAINING")
    print("=" * 50)
    
    # Check dataset
    dataset_available, dataset_path = check_dyslexia_dataset()
    if not dataset_available:
        return None
    
    # Initialize classifier with smaller image size for faster training
    classifier = CNNSVMClassifier(
        img_size=(128, 128),  # Smaller size for faster training
        use_pretrained=True,
        model_type='VGG16'
    )
    
    # Load data (limit samples for faster training)
    print("\n📂 Loading dyslexia detection data...")
    X, y = classifier.load_and_preprocess_data(
        dataset_path, 
        dataset_type='model1',
        max_samples_per_class=400  # Reduced for faster training
    )
    
    print(f"\n📊 Dataset Summary:")
    print(f"   Total images: {X.shape[0]}")
    print(f"   Image shape: {X.shape[1:]}")
    print(f"   Classes: {np.unique(y)}")
    
    # Show class distribution
    unique, counts = np.unique(y, return_counts=True)
    for class_name, count in zip(unique, counts):
        print(f"   {class_name}: {count} images")
    
    # Split data
    print("\n🔄 Splitting data...")
    X_train, X_test, y_train, y_test = train_test_split(
        X, y, test_size=0.2, random_state=42, stratify=y
    )
    
    X_train, X_val, y_train, y_val = train_test_split(
        X_train, y_train, test_size=0.2, random_state=42, stratify=y_train
    )
    
    print(f"   Training set: {X_train.shape[0]} images")
    print(f"   Validation set: {X_val.shape[0]} images")
    print(f"   Test set: {X_test.shape[0]} images")
    
    # Train CNN
    print("\n🧠 Training CNN feature extractor...")
    start_time = time.time()
    
    history = classifier.build_and_train_cnn(
        X_train, y_train, X_val, y_val, epochs=8  # Reduced epochs for faster training
    )
    
    cnn_time = time.time() - start_time
    print(f"⏱️ CNN training completed in {cnn_time:.2f} seconds")
    
    # Train SVM
    print("\n🎯 Training SVM classifier...")
    start_time = time.time()
    
    # Simplified parameter grid for faster training
    param_grid = {
        'C': [1, 10, 100],
        'kernel': ['rbf', 'linear'],
        'gamma': ['scale', 'auto']
    }
    
    best_score = classifier.train_svm(X_train, y_train, param_grid)
    
    svm_time = time.time() - start_time
    print(f"⏱️ SVM training completed in {svm_time:.2f} seconds")
    
    # Test the model
    print("\n📊 Testing dyslexia detection...")
    predictions, probabilities = classifier.predict(X_test)
    
    # Calculate metrics
    accuracy = accuracy_score(y_test, predictions)
    
    print(f"\n🎉 DYSLEXIA DETECTION RESULTS:")
    print(f"   Test Accuracy: {accuracy:.4f} ({accuracy*100:.2f}%)")
    
    # Detailed results
    print(f"\n📋 Classification Report:")
    report = classification_report(y_test, predictions)
    print(report)
    
    # Confusion matrix
    cm = confusion_matrix(y_test, predictions)
    print(f"\n📊 Confusion Matrix:")
    print(f"   True\\Pred  Normal  Dyslexia")
    print(f"   Normal    {cm[1,1]:6d}  {cm[1,0]:8d}")
    print(f"   Dyslexia  {cm[0,1]:6d}  {cm[0,0]:8d}")
    
    # Calculate specific dyslexia detection metrics
    tn, fp, fn, tp = cm.ravel()
    
    dyslexia_precision = tp / (tp + fp) if (tp + fp) > 0 else 0
    dyslexia_recall = tp / (tp + fn) if (tp + fn) > 0 else 0
    
    print(f"\n🔍 Dyslexia Detection Metrics:")
    print(f"   Dyslexia Precision: {dyslexia_precision:.4f}")
    print(f"   Dyslexia Recall: {dyslexia_recall:.4f}")
    print(f"   False Positive Rate: {fp/(fp+tn)*100:.2f}%")
    print(f"   False Negative Rate: {fn/(fn+tp)*100:.2f}%")
    
    # Test individual predictions
    print(f"\n🔍 Testing individual predictions...")
    test_indices = np.random.choice(len(X_test), 5, replace=False)
    correct = 0
    
    for i, idx in enumerate(test_indices):
        sample = X_test[idx:idx+1]
        pred, prob = classifier.predict(sample)
        
        predicted_class = pred[0]
        true_class = y_test[idx]
        dyslexia_prob = prob[0][1] if len(prob[0]) > 1 else 0
        
        is_correct = predicted_class == true_class
        if is_correct:
            correct += 1
        
        status = "✅" if is_correct else "❌"
        print(f"   {status} True: {true_class:8s} | Pred: {predicted_class:8s} | Dyslexia Prob: {dyslexia_prob:.3f}")
    
    print(f"\n   Sample accuracy: {correct}/5 ({correct/5*100:.1f}%)")
    
    # Save model
    print(f"\n💾 Saving dyslexia detection model...")
    classifier.save_model("dyslexia_detector")
    
    print(f"\n🎯 TRAINING COMPLETED!")
    print(f"   Final accuracy: {accuracy:.4f} ({accuracy*100:.2f}%)")
    print(f"   Total training time: {cnn_time + svm_time:.2f} seconds")
    
    return classifier, accuracy

def test_image_for_dyslexia(classifier, image_path):
    """Test a specific image for dyslexia"""
    if not os.path.exists(image_path):
        print(f"❌ Image not found: {image_path}")
        return None
    
    try:
        # Load and preprocess image
        img = classifier.preprocess_image(image_path)
        img_batch = np.expand_dims(img, axis=0)
        
        # Make prediction
        predictions, probabilities = classifier.predict(img_batch)
        
        predicted_class = predictions[0]
        dyslexia_prob = probabilities[0][1] if len(probabilities[0]) > 1 else 0
        normal_prob = probabilities[0][0] if len(probabilities[0]) > 1 else 0
        confidence = np.max(probabilities[0])
        
        print(f"\n🔍 Dyslexia Detection Result:")
        print(f"   Image: {os.path.basename(image_path)}")
        print(f"   Prediction: {predicted_class}")
        print(f"   Is Dyslexia: {'YES' if predicted_class == 'Dyslexia' else 'NO'}")
        print(f"   Confidence: {confidence:.4f}")
        print(f"   Dyslexia Probability: {dyslexia_prob:.4f}")
        print(f"   Normal Probability: {normal_prob:.4f}")
        
        return {
            'prediction': predicted_class,
            'is_dyslexia': predicted_class == 'Dyslexia',
            'confidence': confidence,
            'dyslexia_probability': dyslexia_prob,
            'normal_probability': normal_prob
        }
        
    except Exception as e:
        print(f"❌ Error processing image: {e}")
        return None

def main():
    """Main function"""
    print("🧠 Dyslexia Detection Test")
    print("=" * 40)
    
    # Train the model
    classifier, accuracy = train_dyslexia_detector()
    
    if classifier is None:
        print("❌ Training failed. Please check your dataset.")
        return
    
    # Interpretation
    print(f"\n📈 INTERPRETATION:")
    if accuracy >= 0.85:
        print(f"   ✅ EXCELLENT: Model shows strong dyslexia detection ability")
    elif accuracy >= 0.75:
        print(f"   ✅ GOOD: Model shows good dyslexia detection ability")
    elif accuracy >= 0.65:
        print(f"   ⚠️ MODERATE: Model shows moderate dyslexia detection ability")
    else:
        print(f"   ❌ POOR: Model needs improvement")
    
    print(f"\n💡 USAGE:")
    print(f"   The model can now detect dyslexia characteristics in handwriting")
    print(f"   Use test_image_for_dyslexia(classifier, 'path/to/image.jpg')")
    print(f"   Higher dyslexia probability indicates potential dyslexia traits")
    
    print(f"\n⚠️ IMPORTANT:")
    print(f"   - This is a research tool, not a medical diagnostic device")
    print(f"   - Results should be interpreted by qualified professionals")
    print(f"   - The model is trained on specific handwriting samples")
    
    # Example of testing an image (uncomment to use)
    # test_image_for_dyslexia(classifier, "path/to/your/test/image.jpg")

if __name__ == "__main__":
    main()
