#!/usr/bin/env python3
"""
Training script for CNN-SVM models on handwriting datasets
"""

import os
import numpy as np
import matplotlib.pyplot as plt
from sklearn.model_selection import train_test_split
from cnn_svm_trainer import CNNSVMClassifier
import argparse
import time

def plot_training_history(history, model_name):
    """Plot training history"""
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 4))
    
    # Plot accuracy
    ax1.plot(history.history['accuracy'], label='Training Accuracy')
    if 'val_accuracy' in history.history:
        ax1.plot(history.history['val_accuracy'], label='Validation Accuracy')
    ax1.set_title(f'{model_name} - Model Accuracy')
    ax1.set_xlabel('Epoch')
    ax1.set_ylabel('Accuracy')
    ax1.legend()
    ax1.grid(True)
    
    # Plot loss
    ax2.plot(history.history['loss'], label='Training Loss')
    if 'val_loss' in history.history:
        ax2.plot(history.history['val_loss'], label='Validation Loss')
    ax2.set_title(f'{model_name} - Model Loss')
    ax2.set_xlabel('Epoch')
    ax2.set_ylabel('Loss')
    ax2.legend()
    ax2.grid(True)
    
    plt.tight_layout()
    plt.savefig(f'{model_name.lower()}_training_history.png', dpi=300, bbox_inches='tight')
    plt.show()

def train_model1_normal_vs_dyslexia():
    """Train CNN-SVM for Normal vs Dyslexia classification"""
    print("="*60)
    print("TRAINING MODEL 1: NORMAL vs DYSLEXIA CLASSIFICATION")
    print("="*60)
    
    # Initialize classifier
    classifier = CNNSVMClassifier(
        img_size=(224, 224),
        use_pretrained=True,
        model_type='VGG16'
    )
    
    # Load data
    print("Loading Model 1 dataset...")
    data_path = "Model 1 Normal and dyslexia"
    X, y = classifier.load_and_preprocess_data(data_path, dataset_type='model1')
    
    print(f"Dataset loaded: {X.shape[0]} images")
    print(f"Image shape: {X.shape[1:]}")
    print(f"Classes: {np.unique(y)}")
    print(f"Class distribution: {np.bincount(classifier.label_encoder.fit_transform(y))}")
    
    # Split data
    X_train, X_test, y_train, y_test = train_test_split(
        X, y, test_size=0.2, random_state=42, stratify=y
    )
    
    X_train, X_val, y_train, y_val = train_test_split(
        X_train, y_train, test_size=0.2, random_state=42, stratify=y_train
    )
    
    print(f"Training set: {X_train.shape[0]} images")
    print(f"Validation set: {X_val.shape[0]} images")
    print(f"Test set: {X_test.shape[0]} images")
    
    # Train CNN feature extractor
    print("\nTraining CNN feature extractor...")
    start_time = time.time()
    history = classifier.build_and_train_cnn(
        X_train, y_train, X_val, y_val, epochs=30
    )
    cnn_time = time.time() - start_time
    print(f"CNN training completed in {cnn_time:.2f} seconds")
    
    # Plot training history
    plot_training_history(history, "Model1_CNN")
    
    # Train SVM classifier
    print("\nTraining SVM classifier...")
    start_time = time.time()
    best_score = classifier.train_svm(X_train, y_train)
    svm_time = time.time() - start_time
    print(f"SVM training completed in {svm_time:.2f} seconds")
    
    # Evaluate model
    print("\nEvaluating model on test set...")
    accuracy, report, cm = classifier.evaluate(X_test, y_test)
    
    # Save model
    classifier.save_model("model1_normal_dyslexia")
    
    return classifier, accuracy

def train_model2_alphabet_letters():
    """Train CNN-SVM for A-Z alphabet classification"""
    print("="*60)
    print("TRAINING MODEL 2: ALPHABET LETTERS (A-Z) CLASSIFICATION")
    print("="*60)
    
    # Initialize classifier
    classifier = CNNSVMClassifier(
        img_size=(224, 224),
        use_pretrained=True,
        model_type='VGG16'
    )
    
    # Load data
    print("Loading Model 2 dataset...")
    data_path = "Model 2 Alphabets 26 letters"
    X, y = classifier.load_and_preprocess_data(data_path, dataset_type='model2')
    
    print(f"Dataset loaded: {X.shape[0]} images")
    print(f"Image shape: {X.shape[1:]}")
    print(f"Classes: {np.unique(y)}")
    print(f"Number of classes: {len(np.unique(y))}")
    
    # Split data
    X_train, X_test, y_train, y_test = train_test_split(
        X, y, test_size=0.2, random_state=42, stratify=y
    )
    
    X_train, X_val, y_train, y_val = train_test_split(
        X_train, y_train, test_size=0.2, random_state=42, stratify=y_train
    )
    
    print(f"Training set: {X_train.shape[0]} images")
    print(f"Validation set: {X_val.shape[0]} images")
    print(f"Test set: {X_test.shape[0]} images")
    
    # Train CNN feature extractor
    print("\nTraining CNN feature extractor...")
    start_time = time.time()
    history = classifier.build_and_train_cnn(
        X_train, y_train, X_val, y_val, epochs=30
    )
    cnn_time = time.time() - start_time
    print(f"CNN training completed in {cnn_time:.2f} seconds")
    
    # Plot training history
    plot_training_history(history, "Model2_CNN")
    
    # Train SVM classifier
    print("\nTraining SVM classifier...")
    start_time = time.time()
    
    # Use smaller parameter grid for multi-class problem
    param_grid = {
        'C': [1, 10, 100],
        'kernel': ['rbf', 'linear'],
        'gamma': ['scale', 'auto']
    }
    
    best_score = classifier.train_svm(X_train, y_train, param_grid)
    svm_time = time.time() - start_time
    print(f"SVM training completed in {svm_time:.2f} seconds")
    
    # Evaluate model
    print("\nEvaluating model on test set...")
    accuracy, report, cm = classifier.evaluate(X_test, y_test)
    
    # Save model
    classifier.save_model("model2_alphabet_letters")
    
    return classifier, accuracy

def main():
    """Main training function"""
    parser = argparse.ArgumentParser(description='Train CNN-SVM models for handwriting analysis')
    parser.add_argument('--model', choices=['model1', 'model2', 'both'], default='both',
                       help='Which model to train (default: both)')
    parser.add_argument('--epochs', type=int, default=30,
                       help='Number of epochs for CNN training (default: 30)')
    
    args = parser.parse_args()
    
    results = {}
    
    if args.model in ['model1', 'both']:
        try:
            classifier1, acc1 = train_model1_normal_vs_dyslexia()
            results['Model 1 (Normal vs Dyslexia)'] = acc1
        except Exception as e:
            print(f"Error training Model 1: {e}")
    
    if args.model in ['model2', 'both']:
        try:
            classifier2, acc2 = train_model2_alphabet_letters()
            results['Model 2 (A-Z Letters)'] = acc2
        except Exception as e:
            print(f"Error training Model 2: {e}")
    
    # Print summary
    print("\n" + "="*60)
    print("TRAINING SUMMARY")
    print("="*60)
    for model_name, accuracy in results.items():
        print(f"{model_name}: {accuracy:.4f} ({accuracy*100:.2f}%)")
    
    print("\nTraining completed successfully!")
    print("Models have been saved and can be loaded for inference.")

if __name__ == "__main__":
    main()
