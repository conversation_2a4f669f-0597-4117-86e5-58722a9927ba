# 🧠 Dyslexia Detection Testing Guide

This guide explains how to test images for dyslexia characteristics using the enhanced detection system that supports both single letters and sentence analysis.

## 📁 Files Overview

- **`test_single_image.py`** - Enhanced Python script with both analysis modes
- **`Test_Single_Image.ipynb`** - Interactive Jupyter notebook
- **`image.png`** - Your test image (rename your image to this)

## 🚀 Quick Start

### Method 1: Python Script (Interactive)
```bash
python test_single_image.py
```
The script will ask you to choose:
1. Single Letter/Word Analysis (default)
2. Sentence Analysis (segments into individual letters)

### Method 2: Python Script (Direct Functions)
```python
# For sentence analysis
from test_single_image import test_with_sentence_mode
result = test_with_sentence_mode()

# For single letter analysis  
from test_single_image import test_with_single_mode
result = test_with_single_mode()
```

### Method 3: Jupyter Notebook
1. Open `Test_Single_Image.ipynb`
2. Set `analyze_letters = True` for sentence analysis or `False` for single letter
3. Run all cells

## 🔍 Analysis Modes

### Single Letter/Word Analysis
- **Best for**: Individual letters, short words, or simple images
- **Process**: Analyzes the entire image as one unit
- **Output**: Single prediction with confidence score
- **Speed**: Fast

### Sentence Analysis  
- **Best for**: Sentences, multiple words, or complex handwriting
- **Process**: 
  1. Segments image into individual letters using contour detection
  2. Analyzes each letter separately
  3. Provides comprehensive statistics
- **Output**: Per-letter analysis + overall assessment
- **Speed**: Slower but more detailed

## 📊 Understanding Results

### Single Letter Analysis Results
```
🎯 DYSLEXIA DETECTION RESULTS:
   Prediction: Dyslexia/Normal
   Confidence: 0.8542 (85.4%)
   Dyslexia Probability: 0.7234 (72.3%)
   Normal Probability: 0.2766 (27.7%)
```

### Sentence Analysis Results
```
🎯 SENTENCE ANALYSIS RESULTS:
   Total Letters: 8
   Letters with Dyslexia Characteristics: 3/8
   Average Dyslexia Probability: 0.4521 (45.2%)
   Highest Dyslexia Probability: 0.8234 (82.3%)
   Lowest Dyslexia Probability: 0.1234 (12.3%)
```

## 🎨 Visualizations

### Single Letter Mode
- Original image
- Processed image with prediction
- Probability bar chart

### Sentence Mode
- Original image with letter bounding boxes
- Per-letter probability chart
- Individual letter thumbnails
- Summary statistics (pie chart, statistics)

## 📋 Interpretation Guide

### Probability Ranges
- **🔴 HIGH (>70%)**: Strong dyslexia characteristics
- **🟡 MODERATE (50-70%)**: Some dyslexia characteristics  
- **🟡 LOW (30-50%)**: Mild dyslexia characteristics
- **🟢 NORMAL (<30%)**: Appears normal

### Sentence Analysis Interpretation
- Look for **patterns** across multiple letters
- **3+ letters** with high dyslexia probability may indicate characteristics
- **Average probability >50%** suggests overall dyslexia characteristics
- Individual letter variations are normal

## 🛠️ Technical Requirements

### Prerequisites
```bash
pip install opencv-python matplotlib numpy scikit-learn tensorflow joblib
```

### Model Files Required
- `dyslexia_detector_cnn.h5` - CNN model
- `dyslexia_detector_svm.pkl` - SVM classifier  
- `dyslexia_detector_scaler.pkl` - Feature scaler
- `dyslexia_detector_label_encoder.pkl` - Label encoder

### Image Requirements
- **Format**: PNG, JPG, JPEG
- **Quality**: Clear, good contrast
- **Size**: Any size (will be resized to 128x128)
- **Content**: Handwritten text on light background

## 🔧 Customization

### Change Image Path
```python
# In the script or notebook
image_path = "your_image.png"  # Change this line
```

### Adjust Segmentation Parameters
```python
# In segment_letters_from_sentence function
if w > 10 and h > 15:  # Minimum letter size
    # Adjust these values for different handwriting sizes
```

### Model Path
```python
# Use different model files
result = test_image_for_dyslexia("image.png", model_path_prefix="your_model")
```

## 📈 Best Practices

### For Single Letters
- Use clear, isolated letters
- Ensure good contrast
- Center the letter in the image

### For Sentences
- Use horizontal text
- Avoid overlapping letters
- Ensure consistent spacing
- Good lighting and contrast

### General Tips
- Test multiple samples for better assessment
- Compare results across different writing samples
- Consider both modes for comprehensive analysis

## ⚠️ Important Notes

- **Research Tool**: Not a medical diagnostic device
- **Professional Consultation**: Results should be interpreted by qualified professionals
- **Training Data**: Model trained on specific handwriting samples
- **Limitations**: AI models have inherent limitations and biases

## 🐛 Troubleshooting

### Common Issues

**"No letters detected"**
- Check image contrast
- Ensure text is dark on light background
- Try adjusting segmentation parameters

**"Model files not found"**
- Train the model first using the training notebook
- Check file paths and names

**"Image not found"**
- Verify image filename and path
- Ensure image is in the correct directory

**Poor segmentation**
- Try single letter mode instead
- Improve image quality
- Adjust spacing between letters

## 📞 Support

If you encounter issues:
1. Check the troubleshooting section
2. Verify all requirements are installed
3. Ensure model files are present
4. Test with a simple, clear image first

---

**Remember**: This tool assists in assessment but should never replace professional evaluation by qualified educational or medical professionals.
