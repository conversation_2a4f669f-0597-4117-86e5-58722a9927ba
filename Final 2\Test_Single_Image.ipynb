{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 🧠 Test Image for Dyslexia Detection\n", "\n", "This notebook tests your image for dyslexia characteristics.\n", "Supports both single letter analysis and sentence analysis (segments into individual letters)."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import required libraries\n", "import os\n", "import numpy as np\n", "import cv2\n", "import matplotlib.pyplot as plt\n", "from cnn_svm_trainer import CNNSVMClassifier\n", "import joblib\n", "from tensorflow.keras.models import load_model\n", "\n", "print(\"✅ Libraries imported successfully!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Check if your image exists\n", "image_path = \"image.png\"  # Change this to your image filename\n", "\n", "if os.path.exists(image_path):\n", "    print(f\"✅ Found image: {image_path}\")\n", "    \n", "    # Display the image\n", "    img = cv2.imread(image_path)\n", "    img_rgb = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)\n", "    \n", "    plt.figure(figsize=(10, 6))\n", "    plt.imshow(img_rgb)\n", "    plt.title(f\"Your Image: {image_path}\", fontsize=14, fontweight='bold')\n", "    plt.axis('off')\n", "    plt.show()\n", "    \n", "    print(f\"Image size: {img_rgb.shape}\")\n", "    \n", "    # Determine if this looks like a sentence or single letter\n", "    height, width = img_rgb.shape[:2]\n", "    aspect_ratio = width / height\n", "    \n", "    if aspect_ratio > 3:  # Wide image, likely a sentence\n", "        suggested_mode = \"sentence\"\n", "        print(f\"💡 Suggestion: This appears to be a sentence (aspect ratio: {aspect_ratio:.1f})\")\n", "        print(f\"   Consider using sentence analysis mode for better results.\")\n", "    else:\n", "        suggested_mode = \"single\"\n", "        print(f\"💡 Suggestion: This appears to be a single letter/word (aspect ratio: {aspect_ratio:.1f})\")\n", "        print(f\"   Single letter analysis mode should work well.\")\n", "else:\n", "    print(f\"❌ Image not found: {image_path}\")\n", "    print(f\"Please make sure '{image_path}' is in the same directory as this notebook.\")\n", "    suggested_mode = \"single\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Choose analysis mode\n", "print(\"📋 ANALYSIS MODE SELECTION:\")\n", "print(\"1. Single Letter/Word Analysis - Analyzes the entire image as one unit\")\n", "print(\"2. Sentence Analysis - Segments the image into individual letters and analyzes each\")\n", "print(f\"\\n💡 Suggested mode based on image: {suggested_mode}\")\n", "\n", "# Set analysis mode (change this to True for sentence analysis)\n", "analyze_letters = False  # Set to True for sentence analysis, False for single letter analysis\n", "\n", "if analyze_letters:\n", "    print(\"\\n🔍 Selected: SENTENCE ANALYSIS MODE\")\n", "    print(\"   - Will segment the image into individual letters\")\n", "    print(\"   - Each letter will be analyzed separately\")\n", "    print(\"   - Provides detailed per-letter analysis\")\n", "else:\n", "    print(\"\\n📝 Selected: SINGLE LETTER/WORD ANALYSIS MODE\")\n", "    print(\"   - Will analyze the entire image as one unit\")\n", "    print(\"   - Good for single letters or short words\")\n", "    print(\"   - Faster analysis\")\n", "\n", "print(\"\\n💡 To change mode: modify the 'analyze_letters' variable above and re-run this cell\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Check if trained model exists\n", "model_prefix = \"dyslexia_detector\"\n", "model_files = [\n", "    f\"{model_prefix}_cnn.h5\",\n", "    f\"{model_prefix}_svm.pkl\",\n", "    f\"{model_prefix}_scaler.pkl\",\n", "    f\"{model_prefix}_label_encoder.pkl\"\n", "]\n", "\n", "missing_files = [f for f in model_files if not os.path.exists(f)]\n", "\n", "if not missing_files:\n", "    print(\"✅ All model files found!\")\n", "    for file in model_files:\n", "        print(f\"   ✓ {file}\")\n", "    model_available = True\n", "else:\n", "    print(\"❌ Missing model files:\")\n", "    for file in missing_files:\n", "        print(f\"   ✗ {file}\")\n", "    print(\"\\nPlease train the model first using:\")\n", "    print(\"   - Dyslexia_Detection_Test.ipynb, or\")\n", "    print(\"   - python test_dyslexia_detection.py\")\n", "    model_available = False"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load the trained model\n", "if model_available and os.path.exists(image_path):\n", "    print(\"📂 Loading trained dyslexia detection model...\")\n", "    \n", "    # Initialize classifier\n", "    classifier = CNNSVMClassifier(img_size=(128, 128))\n", "    \n", "    # Load model components\n", "    classifier.cnn_model = load_model(f\"{model_prefix}_cnn.h5\")\n", "    classifier.svm_model = joblib.load(f\"{model_prefix}_svm.pkl\")\n", "    classifier.scaler = joblib.load(f\"{model_prefix}_scaler.pkl\")\n", "    classifier.label_encoder = joblib.load(f\"{model_prefix}_label_encoder.pkl\")\n", "    \n", "    print(\"✅ Model loaded successfully!\")\n", "    print(f\"   Classes: {classifier.label_encoder.classes_}\")\n", "else:\n", "    print(\"❌ Cannot proceed without model or image.\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Test your image for dyslexia\n", "if model_available and os.path.exists(image_path) and 'classifier' in locals():\n", "    print(f\"🧠 Testing {image_path} for dyslexia characteristics...\")\n", "    print(f\"Analysis Mode: {'Sentence Analysis' if analyze_letters else 'Single Letter/Word Analysis'}\")\n", "    \n", "    # Load image\n", "    img = cv2.imread(image_path)\n", "    img_rgb = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)\n", "    \n", "    # Use the appropriate analysis function\n", "    if analyze_letters:\n", "        # Import the analysis functions\n", "        from test_single_image import analyze_sentence_letters\n", "        test_results = analyze_sentence_letters(img_rgb, classifier, image_path)\n", "    else:\n", "        # Import the analysis functions  \n", "        from test_single_image import analyze_single_image\n", "        test_results = analyze_single_image(img_rgb, classifier, image_path)\n", "    \n", "    print(f\"\\n✅ Analysis completed!\")\n", "else:\n", "    print(\"❌ Cannot test image without model and image file.\")\n", "    test_results = None"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Results are automatically visualized by the analysis functions\n", "# The visualizations are already shown above\n", "if test_results:\n", "    print(f\"\\n✅ Analysis completed for {image_path}!\")\n", "    print(f\"\\n📊 QUICK SUMMARY:\")\n", "    \n", "    if test_results.get('type') == 'sentence_analysis':\n", "        print(f\"   Analysis Type: Sentence Analysis\")\n", "        print(f\"   Total Letters: {test_results['total_letters']}\")\n", "        print(f\"   Letters with Dyslexia Characteristics: {test_results['dyslexia_letters']}\")\n", "        print(f\"   Average Dyslexia Probability: {test_results['average_dyslexia_probability']:.1%}\")\n", "        print(f\"   Overall Assessment: {test_results['overall_assessment']}\")\n", "    else:\n", "        print(f\"   Analysis Type: Single Letter/Word Analysis\")\n", "        print(f\"   Prediction: {test_results['prediction']}\")\n", "        print(f\"   Dyslexia Probability: {test_results['dyslexia_probability']:.1%}\")\n", "        print(f\"   Confidence: {test_results['confidence']:.1%}\")\n", "else:\n", "    print(\"❌ No results to display.\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Summary and important notes\n", "if test_results:\n", "    print(\"📋 FINAL SUMMARY\")\n", "    print(\"=\" * 40)\n", "    print(f\"Image: {image_path}\")\n", "    print(f\"Analysis Mode: {'Sentence Analysis' if analyze_letters else 'Single Letter/Word Analysis'}\")\n", "    \n", "    if test_results.get('type') == 'sentence_analysis':\n", "        print(f\"Total Letters Analyzed: {test_results['total_letters']}\")\n", "        print(f\"Letters with Dyslexia Characteristics: {test_results['dyslexia_letters']}\")\n", "        print(f\"Average Dyslexia Probability: {test_results['average_dyslexia_probability']:.1%}\")\n", "        print(f\"Overall Assessment: {test_results['overall_assessment']}\")\n", "        high_dyslexia = test_results['average_dyslexia_probability'] > 0.5\n", "    else:\n", "        print(f\"Prediction: {test_results['prediction']}\")\n", "        print(f\"Dyslexia Probability: {test_results['dyslexia_probability']:.1%}\")\n", "        print(f\"Confidence: {test_results['confidence']:.1%}\")\n", "        high_dyslexia = test_results['dyslexia_probability'] > 0.5\n", "    \n", "    print(f\"\\n⚠️ IMPORTANT NOTES:\")\n", "    print(f\"   - This is a research tool, not a medical diagnostic device\")\n", "    print(f\"   - Results should be interpreted by qualified professionals\")\n", "    print(f\"   - The model is trained on specific handwriting samples\")\n", "    \n", "    if analyze_letters:\n", "        print(f\"   - Sentence analysis provides comprehensive per-letter assessment\")\n", "        print(f\"   - Individual letter variations are normal in handwriting\")\n", "        print(f\"   - Look for patterns across multiple letters\")\n", "    else:\n", "        print(f\"   - Single letter analysis may be less reliable than sentence analysis\")\n", "        print(f\"   - Consider testing with sentence analysis for more detailed results\")\n", "    \n", "    print(f\"   - Consider testing with multiple handwriting samples for better assessment\")\n", "    \n", "    if high_dyslexia:\n", "        print(f\"\\n💡 RECOMMENDATIONS:\")\n", "        print(f\"   - Test with more handwriting samples\")\n", "        if not analyze_letters:\n", "            print(f\"   - Try sentence analysis mode for detailed per-letter assessment\")\n", "        print(f\"   - Consider testing with different types of writing (letters, words, sentences)\")\n", "        print(f\"   - Consult with educational or medical professionals\")\n", "        print(f\"   - Remember: This tool assists assessment but doesn't replace professional evaluation\")\n", "else:\n", "    print(\"❌ No analysis was performed.\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}