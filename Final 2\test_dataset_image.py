#!/usr/bin/env python3
"""
Dataset Dyslexia Detection - Single Image Testing (CNN + SVM)
This script tests individual images using both CNN and SVM models trained on the dataset folder.
"""

import cv2
import numpy as np
import matplotlib.pyplot as plt
import tensorflow as tf
from tensorflow.keras.models import load_model
import joblib
import os
import sys
from matplotlib.patches import Rectangle

def detect_letter_from_filename(image_path):
    """
    Try to detect what letter this is from the filename
    """
    filename = os.path.basename(image_path).upper()
    
    # Common letter patterns in filenames
    letters = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 
               'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z']
    
    # Check if filename starts with a letter
    for letter in letters:
        if (filename.startswith(letter + '.') or 
            filename.startswith(letter + '_') or 
            filename == letter + '.JPG' or 
            filename == letter + '.PNG'):
            return letter
    
    # Check if letter is anywhere in filename
    for letter in letters:
        if letter in filename:
            return letter
    
    return "Unknown"

def segment_letters_from_sentence(image_path, min_width=20, min_height=30):
    """
    Segment individual letters from a sentence image
    Returns list of letter images and their positions
    """
    # Load image
    image = cv2.imread(image_path)
    if image is None:
        return [], []

    # Convert to grayscale
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

    # Apply threshold to get binary image
    _, binary = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)

    # Find contours
    contours, _ = cv2.findContours(binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

    # Filter and sort contours by x-coordinate (left to right)
    letter_contours = []
    for contour in contours:
        x, y, w, h = cv2.boundingRect(contour)
        if w >= min_width and h >= min_height:
            letter_contours.append((x, y, w, h))

    # Sort by x-coordinate (left to right)
    letter_contours.sort(key=lambda x: x[0])

    # Extract letter images
    letter_images = []
    positions = []

    for i, (x, y, w, h) in enumerate(letter_contours):
        # Add some padding
        padding = 5
        x_start = max(0, x - padding)
        y_start = max(0, y - padding)
        x_end = min(image.shape[1], x + w + padding)
        y_end = min(image.shape[0], y + h + padding)

        # Extract letter region
        letter_img = image[y_start:y_end, x_start:x_end]

        if letter_img.size > 0:
            letter_images.append(letter_img)
            positions.append((x, y, w, h, i))

    return letter_images, positions

def test_single_image_cnn_svm(image_path, model_prefix='dataset_dyslexia_detector'):
    """
    Test a single image for dyslexia detection using both CNN and SVM
    """
    print(f"🔍 Testing image: {os.path.basename(image_path)}")
    print(f"📁 Full path: {image_path}")
    
    # Check if image exists
    if not os.path.exists(image_path):
        print(f"❌ Image not found: {image_path}")
        return None
    
    # Check if models exist
    cnn_path = f"{model_prefix}_cnn.h5"
    svm_path = f"{model_prefix}_svm.pkl"
    scaler_path = f"{model_prefix}_scaler.pkl"
    encoder_path = f"{model_prefix}_label_encoder.pkl"
    
    missing_files = []
    for file_path in [cnn_path, svm_path, scaler_path, encoder_path]:
        if not os.path.exists(file_path):
            missing_files.append(file_path)
    
    if missing_files:
        print(f"❌ Model files not found:")
        for file in missing_files:
            print(f"   - {file}")
        print(f"\n💡 Make sure you have completed training in Dataset_Training.ipynb")
        return None
    
    try:
        # Load models
        print(f"\n📂 Loading models...")
        cnn_model = load_model(cnn_path)
        svm_model = joblib.load(svm_path)
        scaler = joblib.load(scaler_path)
        label_encoder = joblib.load(encoder_path)
        print(f"   ✅ All models loaded successfully!")
        
        # Load and preprocess image
        print(f"\n🖼️ Processing image...")
        image = cv2.imread(image_path)
        if image is None:
            print(f"❌ Could not load image: {image_path}")
            return None
        
        # Convert BGR to RGB
        image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        
        # Resize image
        image_resized = cv2.resize(image_rgb, (128, 128))
        
        # Normalize
        image_normalized = image_resized.astype('float32') / 255.0
        
        # Add batch dimension
        image_batch = np.expand_dims(image_normalized, axis=0)
        
        print(f"   ✅ Image processed successfully!")
        
        # Create feature extractor
        print(f"\n🔍 Extracting features...")
        feature_model = tf.keras.Sequential([
            layer for layer in cnn_model.layers[:-1]
        ])
        feature_model.build(input_shape=(None, 128, 128, 3))
        
        # Extract features
        features = feature_model.predict(image_batch, verbose=0)
        features_scaled = scaler.transform(features)
        
        # Get predictions
        print(f"\n🎯 Making predictions...")
        cnn_pred = cnn_model.predict(image_batch, verbose=0)[0]
        svm_pred = svm_model.predict_proba(features_scaled)[0]
        
        # Get class labels
        classes = label_encoder.classes_
        
        # Detect letter from filename
        detected_letter = detect_letter_from_filename(image_path)
        
        # Display results
        print(f"\n📊 Displaying results...")
        fig, axes = plt.subplots(1, 2, figsize=(15, 6))
        
        # Show original image
        axes[0].imshow(image_rgb)
        axes[0].set_title('Original Image', fontsize=14, fontweight='bold')
        axes[0].axis('off')
        
        # Show processed image
        axes[1].imshow(image_resized)
        axes[1].set_title('Processed Image (128x128)', fontsize=14, fontweight='bold')
        axes[1].axis('off')
        
        plt.tight_layout()
        plt.show()
        
        # Print detailed results
        print(f"\n" + "="*60)
        print(f"🔍 DYSLEXIA DETECTION RESULTS")
        print(f"="*60)
        print(f"📁 Image: {os.path.basename(image_path)}")
        print(f"🔤 Detected Letter: {detected_letter}")
        print(f"🤖 Model: {model_prefix} (CNN + SVM)")
        
        print(f"\n📊 CNN Predictions:")
        for i, class_name in enumerate(classes):
            confidence = cnn_pred[i] * 100
            print(f"   {class_name}: {confidence:.1f}%")
        
        print(f"\n🎯 SVM Predictions:")
        for i, class_name in enumerate(classes):
            confidence = svm_pred[i] * 100
            print(f"   {class_name}: {confidence:.1f}%")
        
        # Final predictions
        cnn_prediction = classes[np.argmax(cnn_pred)]
        cnn_confidence = np.max(cnn_pred) * 100
        svm_prediction = classes[np.argmax(svm_pred)]
        svm_confidence = np.max(svm_pred) * 100
        
        print(f"\n🏆 FINAL PREDICTIONS:")
        print(f"   CNN: {cnn_prediction.upper()} ({cnn_confidence:.1f}%)")
        print(f"   SVM: {svm_prediction.upper()} ({svm_confidence:.1f}%)")
        
        # Combined analysis
        print(f"\n📝 ANALYSIS:")
        if detected_letter != "Unknown":
            print(f"   Letter '{detected_letter}' Analysis:")
            
            # Check if both models agree
            if cnn_prediction.lower() == svm_prediction.lower():
                if svm_prediction.lower() == 'dyslexia':
                    print(f"   🔴 Both models agree: Letter '{detected_letter}' shows DYSLEXIA characteristics")
                    avg_confidence = (cnn_confidence + svm_confidence) / 2
                    if avg_confidence > 80:
                        print(f"   🔴 HIGH confidence ({avg_confidence:.1f}%): Strong dyslexia indicators")
                    elif avg_confidence > 60:
                        print(f"   🟡 MODERATE confidence ({avg_confidence:.1f}%): Some dyslexia indicators")
                    else:
                        print(f"   🟢 LOW confidence ({avg_confidence:.1f}%): Weak dyslexia indicators")
                else:
                    print(f"   ✅ Both models agree: Letter '{detected_letter}' appears NORMAL")
                    avg_confidence = (cnn_confidence + svm_confidence) / 2
                    print(f"   ✅ Confidence: {avg_confidence:.1f}%")
            else:
                print(f"   ⚠️ Models disagree:")
                print(f"      CNN says: {cnn_prediction.upper()} ({cnn_confidence:.1f}%)")
                print(f"      SVM says: {svm_prediction.upper()} ({svm_confidence:.1f}%)")
                print(f"   💡 Recommend: Use SVM result as final decision (more reliable)")
                
                if svm_prediction.lower() == 'dyslexia':
                    print(f"   🔴 Final: Letter '{detected_letter}' shows DYSLEXIA characteristics")
                else:
                    print(f"   ✅ Final: Letter '{detected_letter}' appears NORMAL")
        else:
            # Generic analysis when letter is unknown
            if cnn_prediction.lower() == svm_prediction.lower():
                if svm_prediction.lower() == 'dyslexia':
                    print(f"   🔴 Both models agree: Image shows DYSLEXIA characteristics")
                else:
                    print(f"   ✅ Both models agree: Image appears NORMAL")
            else:
                print(f"   ⚠️ Models disagree - using SVM as final decision")
                if svm_prediction.lower() == 'dyslexia':
                    print(f"   🔴 Final: Image shows DYSLEXIA characteristics")
                else:
                    print(f"   ✅ Final: Image appears NORMAL")
        
        print(f"\n⚠️ IMPORTANT: This is a research tool, not a medical diagnostic device")
        print(f"="*60)
        
        # Return results for further processing if needed
        return {
            'image_path': image_path,
            'detected_letter': detected_letter,
            'cnn_prediction': cnn_prediction,
            'cnn_confidence': cnn_confidence,
            'svm_prediction': svm_prediction,
            'svm_confidence': svm_confidence,
            'classes': classes,
            'cnn_probs': cnn_pred,
            'svm_probs': svm_pred
        }
        
    except Exception as e:
        print(f"❌ Error during prediction: {str(e)}")
        import traceback
        traceback.print_exc()
        return None

def analyze_sentence_image(image_path, model_prefix='dataset_dyslexia_detector'):
    """
    Analyze a sentence image by segmenting letters and predicting each one
    Similar to your dyslexia detection system
    """
    print(f"📝 Analyzing sentence: {os.path.basename(image_path)}")

    # Check if image exists
    if not os.path.exists(image_path):
        print(f"❌ Image not found: {image_path}")
        return None

    # Check if models exist
    cnn_path = f"{model_prefix}_cnn.h5"
    svm_path = f"{model_prefix}_svm.pkl"
    scaler_path = f"{model_prefix}_scaler.pkl"
    encoder_path = f"{model_prefix}_label_encoder.pkl"

    missing_files = []
    for file_path in [cnn_path, svm_path, scaler_path, encoder_path]:
        if not os.path.exists(file_path):
            missing_files.append(file_path)

    if missing_files:
        print(f"❌ Model files not found:")
        for file in missing_files:
            print(f"   - {file}")
        return None

    try:
        # Load models
        print(f"📂 Loading models...")
        cnn_model = load_model(cnn_path)
        svm_model = joblib.load(svm_path)
        scaler = joblib.load(scaler_path)
        label_encoder = joblib.load(encoder_path)
        classes = label_encoder.classes_

        # Segment letters from sentence
        print(f"✂️ Segmenting letters...")
        letter_images, positions = segment_letters_from_sentence(image_path)

        if not letter_images:
            print(f"❌ No letters found in image")
            return None

        print(f"   ✅ Found {len(letter_images)} letters")

        # Create feature extractor
        feature_model = tf.keras.Sequential([
            layer for layer in cnn_model.layers[:-1]
        ])
        feature_model.build(input_shape=(None, 128, 128, 3))

        # Analyze each letter
        letter_results = []
        dyslexia_count = 0

        for i, letter_img in enumerate(letter_images):
            # Preprocess letter image
            letter_rgb = cv2.cvtColor(letter_img, cv2.COLOR_BGR2RGB)
            letter_resized = cv2.resize(letter_rgb, (128, 128))
            letter_normalized = letter_resized.astype('float32') / 255.0
            letter_batch = np.expand_dims(letter_normalized, axis=0)

            # Extract features and predict
            features = feature_model.predict(letter_batch, verbose=0)
            features_scaled = scaler.transform(features)

            cnn_pred = cnn_model.predict(letter_batch, verbose=0)[0]
            svm_pred = svm_model.predict_proba(features_scaled)[0]

            # Get final prediction (use SVM as primary)
            svm_prediction = classes[np.argmax(svm_pred)]
            svm_confidence = np.max(svm_pred)

            if svm_prediction.lower() == 'dyslexia':
                dyslexia_count += 1

            letter_results.append({
                'index': i,
                'image': letter_resized,
                'cnn_pred': cnn_pred,
                'svm_pred': svm_pred,
                'svm_prediction': svm_prediction,
                'svm_confidence': svm_confidence,
                'position': positions[i] if i < len(positions) else None
            })

        # Calculate overall assessment
        dyslexia_percentage = (dyslexia_count / len(letter_images)) * 100

        if dyslexia_percentage > 70:
            overall_assessment = "High dyslexia likelihood"
        elif dyslexia_percentage > 40:
            overall_assessment = "Moderate dyslexia likelihood"
        elif dyslexia_percentage > 20:
            overall_assessment = "Low dyslexia likelihood"
        else:
            overall_assessment = "Normal handwriting"

        # Create visualization
        print(f"📊 Creating visualization...")

        # Load original image for display
        original_img = cv2.imread(image_path)
        original_rgb = cv2.cvtColor(original_img, cv2.COLOR_BGR2RGB)

        # Create figure with subplots
        fig = plt.figure(figsize=(16, 12))

        # Top: Original image with bounding boxes
        ax1 = plt.subplot(3, 1, 1)
        ax1.imshow(original_rgb)
        ax1.set_title(f'Sentence Analysis: {os.path.basename(image_path)}\nOverall Assessment: {overall_assessment}',
                     fontsize=14, fontweight='bold')
        ax1.axis('off')

        # Add bounding boxes around letters
        for result in letter_results:
            if result['position']:
                x, y, w, h, idx = result['position']
                color = 'red' if result['svm_prediction'].lower() == 'dyslexia' else 'green'
                rect = Rectangle((x, y), w, h, linewidth=2, edgecolor=color, facecolor='none')
                ax1.add_patch(rect)

                # Add letter index
                ax1.text(x, y-5, f"L{idx+1}", fontsize=10, color=color, fontweight='bold')

        # Middle: Dyslexia probability bar chart
        ax2 = plt.subplot(3, 1, 2)
        letter_indices = [f"L{i+1}" for i in range(len(letter_results))]
        dyslexia_probs = [result['svm_pred'][1] if len(result['svm_pred']) > 1 else result['svm_pred'][0]
                         for result in letter_results]

        colors = ['red' if prob > 0.5 else 'green' for prob in dyslexia_probs]
        bars = ax2.bar(letter_indices, dyslexia_probs, color=colors, alpha=0.7)

        # Add probability values on top of bars
        for i, (bar, prob) in enumerate(zip(bars, dyslexia_probs)):
            height = bar.get_height()
            ax2.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                    f'{prob:.2f}', ha='center', va='bottom', fontweight='bold')

        ax2.set_ylabel('Dyslexia Probability')
        ax2.set_title('Dyslexia Probability by Letter', fontweight='bold')
        ax2.set_ylim(0, 1.1)
        ax2.axhline(y=0.5, color='black', linestyle='--', alpha=0.5)

        # Bottom: Individual letter images
        ax3 = plt.subplot(3, 1, 3)

        # Create a grid of letter images
        if letter_results:
            # Calculate grid dimensions
            n_letters = len(letter_results)
            cols = min(10, n_letters)  # Max 10 columns
            rows = (n_letters + cols - 1) // cols

            # Create combined image
            letter_height = 60
            letter_width = 60
            combined_img = np.ones((rows * letter_height, cols * letter_width, 3), dtype=np.uint8) * 255

            for i, result in enumerate(letter_results):
                row = i // cols
                col = i % cols

                # Resize letter image
                letter_resized = cv2.resize(result['image'], (letter_width-10, letter_height-10))

                # Place in combined image
                y_start = row * letter_height + 5
                y_end = y_start + letter_height - 10
                x_start = col * letter_width + 5
                x_end = x_start + letter_width - 10

                combined_img[y_start:y_end, x_start:x_end] = letter_resized

            ax3.imshow(combined_img)
            ax3.set_title('Individual Letters', fontweight='bold')
            ax3.axis('off')

        plt.tight_layout()
        plt.show()

        # Print detailed results
        print(f"\n" + "="*80)
        print(f"📝 SENTENCE ANALYSIS RESULTS")
        print(f"="*80)
        print(f"📁 Image: {os.path.basename(image_path)}")
        print(f"🔤 Total Letters: {len(letter_images)}")
        print(f"🔴 Dyslexic Letters: {dyslexia_count}")
        print(f"🟢 Normal Letters: {len(letter_images) - dyslexia_count}")
        print(f"📊 Dyslexia Percentage: {dyslexia_percentage:.1f}%")
        print(f"🎯 Overall Assessment: {overall_assessment}")

        print(f"\n📋 Letter-by-Letter Analysis:")
        for i, result in enumerate(letter_results):
            status = "🔴 DYSLEXIA" if result['svm_prediction'].lower() == 'dyslexia' else "🟢 NORMAL"
            confidence = result['svm_confidence'] * 100
            print(f"   L{i+1}: {status} ({confidence:.1f}%)")

        print(f"\n⚠️ IMPORTANT: This is a research tool, not a medical diagnostic device")
        print(f"="*80)

        return {
            'image_path': image_path,
            'total_letters': len(letter_images),
            'dyslexic_letters': dyslexia_count,
            'normal_letters': len(letter_images) - dyslexia_count,
            'dyslexia_percentage': dyslexia_percentage,
            'overall_assessment': overall_assessment,
            'letter_results': letter_results
        }

    except Exception as e:
        print(f"❌ Error during analysis: {str(e)}")
        import traceback
        traceback.print_exc()
        return None

def main():
    """
    Main function for command line usage
    """
    if len(sys.argv) < 2:
        print("Usage: python test_dataset_image.py <image_path> [mode]")
        print("Examples:")
        print("  python test_dataset_image.py 23.jpg single    # Test single letter")
        print("  python test_dataset_image.py sentence.jpg sentence  # Analyze sentence")
        print("  python test_dataset_image.py 23.jpg          # Auto-detect mode")
        sys.exit(1)

    image_path = sys.argv[1]
    mode = sys.argv[2] if len(sys.argv) > 2 else "auto"

    if mode == "sentence":
        print("🔍 Running sentence analysis mode...")
        result = analyze_sentence_image(image_path)
    elif mode == "single":
        print("🔍 Running single letter mode...")
        result = test_single_image_cnn_svm(image_path)
    else:
        # Auto-detect mode based on filename or user choice
        print("🤖 Auto-detecting mode...")
        print("Choose analysis mode:")
        print("1. Single letter analysis")
        print("2. Sentence analysis")

        try:
            choice = input("Enter choice (1 or 2): ").strip()
            if choice == "2":
                print("🔍 Running sentence analysis mode...")
                result = analyze_sentence_image(image_path)
            else:
                print("🔍 Running single letter mode...")
                result = test_single_image_cnn_svm(image_path)
        except KeyboardInterrupt:
            print("\n👋 Goodbye!")
            sys.exit(0)

    if result is None:
        sys.exit(1)

if __name__ == "__main__":
    # Option 1: Use command line arguments
    if len(sys.argv) > 1:
        main()
    else:
        # Option 2: Test a specific image directly (change the path below)
        image_path = "image.png"  # ← Change this to your image path

        print(f"🎯 Testing image directly: {image_path}")
        print("Choose analysis mode:")
        print("1. Single letter analysis")
        print("2. Sentence analysis (like your example)")

        try:
            choice = input("Enter choice (1 or 2): ").strip()
            if choice == "2":
                print("🔍 Running sentence analysis...")
                result = analyze_sentence_image(image_path)
            else:
                print("🔍 Running single letter analysis...")
                result = test_single_image_cnn_svm(image_path)
        except KeyboardInterrupt:
            print("\n👋 Goodbye!")

        # Option 3: Test multiple images (uncomment and modify as needed)
        # image_list = ["23.jpg", "A.jpg", "B.jpg"]
        # for img in image_list:
        #     print(f"\n{'='*60}")
        #     result = test_single_image_cnn_svm(img)
