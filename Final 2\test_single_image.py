#!/usr/bin/env python3
"""
Test images for dyslexia detection - supports both single letters and sentences
"""

import os
import numpy as np
import cv2
import matplotlib.pyplot as plt
from cnn_svm_trainer import CNNSVMClassifier
import joblib
from tensorflow.keras.models import load_model
from sklearn.cluster import DBSCAN
import string

def segment_letters_from_sentence(image):
    """
    Segment individual letters from a sentence image using contour detection

    Args:
        image: Input image (RGB format)

    Returns:
        List of letter images and their bounding boxes
    """
    # Convert to grayscale
    gray = cv2.cvtColor(image, cv2.COLOR_RGB2GRAY)

    # Apply threshold to get binary image
    _, binary = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)

    # Find contours
    contours, _ = cv2.findContours(binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

    # Filter and sort contours by x-coordinate (left to right)
    letter_contours = []
    for contour in contours:
        x, y, w, h = cv2.boundingRect(contour)
        # Filter out very small or very large contours
        if w > 10 and h > 15 and w < image.shape[1] * 0.8 and h < image.shape[0] * 0.8:
            letter_contours.append((x, y, w, h, contour))

    # Sort by x-coordinate (left to right)
    letter_contours.sort(key=lambda x: x[0])

    # Extract letter images
    letter_images = []
    bounding_boxes = []

    for x, y, w, h, contour in letter_contours:
        # Add some padding
        padding = 5
        x_start = max(0, x - padding)
        y_start = max(0, y - padding)
        x_end = min(image.shape[1], x + w + padding)
        y_end = min(image.shape[0], y + h + padding)

        # Extract letter region
        letter_img = image[y_start:y_end, x_start:x_end]

        if letter_img.size > 0:
            letter_images.append(letter_img)
            bounding_boxes.append((x_start, y_start, x_end - x_start, y_end - y_start))

    return letter_images, bounding_boxes

def test_image_for_dyslexia(image_path, model_path_prefix="dyslexia_detector", analyze_letters=False):
    """
    Test an image for dyslexia detection - supports both single letters and sentences

    Args:
        image_path: Path to the image file
        model_path_prefix: Prefix of the saved model files
        analyze_letters: If True, segment sentence into individual letters for analysis
    """
    
    # Check if image exists
    if not os.path.exists(image_path):
        print(f"❌ Image not found: {image_path}")
        return None
    
    # Check if model files exist
    model_files = [
        f"{model_path_prefix}_cnn.h5",
        f"{model_path_prefix}_svm.pkl",
        f"{model_path_prefix}_scaler.pkl",
        f"{model_path_prefix}_label_encoder.pkl"
    ]
    
    missing_files = [f for f in model_files if not os.path.exists(f)]
    if missing_files:
        print(f"❌ Model files not found: {missing_files}")
        print("Please train the model first using the notebook or training script.")
        return None
    
    try:
        # Initialize classifier
        classifier = CNNSVMClassifier(img_size=(128, 128))
        
        # Load the trained model
        print("📂 Loading trained dyslexia detection model...")
        classifier.cnn_model = load_model(f"{model_path_prefix}_cnn.h5")
        classifier.svm_model = joblib.load(f"{model_path_prefix}_svm.pkl")
        classifier.scaler = joblib.load(f"{model_path_prefix}_scaler.pkl")
        classifier.label_encoder = joblib.load(f"{model_path_prefix}_label_encoder.pkl")
        
        print("✅ Model loaded successfully!")
        
        # Load and preprocess the image
        print(f"🖼️ Processing image: {os.path.basename(image_path)}")

        # Load image
        img = cv2.imread(image_path)
        if img is None:
            print(f"❌ Could not load image: {image_path}")
            return None

        # Convert to RGB
        img_rgb = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)

        # Check if we should analyze individual letters
        if analyze_letters:
            return analyze_sentence_letters(img_rgb, classifier, image_path)
        else:
            return analyze_single_image(img_rgb, classifier, image_path)
        
    except Exception as e:
        print(f"❌ Error processing image: {e}")
        return None

def analyze_single_image(img_rgb, classifier, image_path):
    """
    Analyze a single image (letter or word) for dyslexia detection
    """
    # Resize and normalize
    img_resized = cv2.resize(img_rgb, classifier.img_size)
    img_normalized = img_resized.astype('float32') / 255.0
    img_batch = np.expand_dims(img_normalized, axis=0)

    # Make prediction
    print("🧠 Making prediction...")
    predictions, probabilities = classifier.predict(img_batch)

    # Extract results
    predicted_class = predictions[0]
    confidence = np.max(probabilities[0])

    # Get class probabilities
    classes = classifier.label_encoder.classes_
    class_probs = dict(zip(classes, probabilities[0]))

    # Determine dyslexia probability
    if 'Dyslexia' in class_probs:
        dyslexia_prob = class_probs['Dyslexia']
        normal_prob = class_probs.get('Normal', 1 - dyslexia_prob)
    else:
        dyslexia_prob = 0.0
        normal_prob = 1.0

    # Display results
    print(f"\n🎯 DYSLEXIA DETECTION RESULTS:")
    print(f"   Image: {os.path.basename(image_path)}")
    print(f"   Prediction: {predicted_class}")
    print(f"   Is Dyslexia: {'YES' if predicted_class == 'Dyslexia' else 'NO'}")
    print(f"   Confidence: {confidence:.4f} ({confidence*100:.1f}%)")
    print(f"   Dyslexia Probability: {dyslexia_prob:.4f} ({dyslexia_prob*100:.1f}%)")
    print(f"   Normal Probability: {normal_prob:.4f} ({normal_prob*100:.1f}%)")

    # Interpretation
    print(f"\n📊 INTERPRETATION:")
    if dyslexia_prob > 0.7:
        print(f"   🔴 HIGH: Strong indication of dyslexia characteristics")
    elif dyslexia_prob > 0.5:
        print(f"   🟡 MODERATE: Some dyslexia characteristics detected")
    elif dyslexia_prob > 0.3:
        print(f"   🟡 LOW: Mild dyslexia characteristics")
    else:
        print(f"   🟢 NORMAL: Handwriting appears normal")

    # Visualize the image and results
    plt.figure(figsize=(12, 6))

    # Original image
    plt.subplot(1, 2, 1)
    plt.imshow(img_rgb)
    plt.title(f"Original Image\n{os.path.basename(image_path)}", fontsize=12, fontweight='bold')
    plt.axis('off')

    # Processed image with results
    plt.subplot(1, 2, 2)
    plt.imshow(img_resized)

    # Color based on prediction
    title_color = 'red' if predicted_class == 'Dyslexia' else 'green'
    plt.title(f"Prediction: {predicted_class}\nConfidence: {confidence:.3f}\nDyslexia Prob: {dyslexia_prob:.3f}",
             color=title_color, fontsize=12, fontweight='bold')
    plt.axis('off')

    plt.tight_layout()
    plt.show()

    # Create probability bar chart
    plt.figure(figsize=(8, 5))
    classes_list = list(class_probs.keys())
    probs_list = list(class_probs.values())
    colors = ['red' if cls == 'Dyslexia' else 'green' for cls in classes_list]

    bars = plt.bar(classes_list, probs_list, color=colors, alpha=0.7)
    plt.title('Dyslexia Detection Probabilities', fontsize=14, fontweight='bold')
    plt.ylabel('Probability')
    plt.ylim(0, 1)

    # Add value labels on bars
    for bar, prob in zip(bars, probs_list):
        plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                f'{prob:.3f}', ha='center', va='bottom', fontweight='bold')

    plt.tight_layout()
    plt.show()

    return {
        'prediction': predicted_class,
        'is_dyslexia': predicted_class == 'Dyslexia',
        'confidence': confidence,
        'dyslexia_probability': dyslexia_prob,
        'normal_probability': normal_prob,
        'all_probabilities': class_probs
    }

def analyze_sentence_letters(img_rgb, classifier, image_path):
    """
    Analyze individual letters in a sentence for dyslexia detection
    """
    print(f"🔍 Segmenting sentence into individual letters...")

    # Segment letters from the sentence
    letter_images, bounding_boxes = segment_letters_from_sentence(img_rgb)

    if not letter_images:
        print("❌ No letters detected in the image. Try adjusting the image or use single letter mode.")
        return None

    print(f"✅ Found {len(letter_images)} letters in the sentence")

    # Analyze each letter
    letter_results = []
    dyslexia_scores = []

    for i, letter_img in enumerate(letter_images):
        print(f"\n📝 Analyzing letter {i+1}/{len(letter_images)}...")

        # Resize and normalize letter
        letter_resized = cv2.resize(letter_img, classifier.img_size)
        letter_normalized = letter_resized.astype('float32') / 255.0
        letter_batch = np.expand_dims(letter_normalized, axis=0)

        # Make prediction
        predictions, probabilities = classifier.predict(letter_batch)

        # Extract results
        predicted_class = predictions[0]
        confidence = np.max(probabilities[0])

        # Get class probabilities
        classes = classifier.label_encoder.classes_
        class_probs = dict(zip(classes, probabilities[0]))

        # Determine dyslexia probability
        dyslexia_prob = class_probs.get('Dyslexia', 0.0)
        normal_prob = class_probs.get('Normal', 1.0 - dyslexia_prob)

        letter_result = {
            'letter_index': i + 1,
            'prediction': predicted_class,
            'confidence': confidence,
            'dyslexia_probability': dyslexia_prob,
            'normal_probability': normal_prob,
            'bounding_box': bounding_boxes[i],
            'letter_image': letter_resized
        }

        letter_results.append(letter_result)
        dyslexia_scores.append(dyslexia_prob)

        print(f"   Letter {i+1}: {predicted_class} (Dyslexia: {dyslexia_prob:.3f})")

    # Calculate overall statistics
    avg_dyslexia_prob = np.mean(dyslexia_scores)
    max_dyslexia_prob = np.max(dyslexia_scores)
    min_dyslexia_prob = np.min(dyslexia_scores)
    dyslexia_letters = sum(1 for score in dyslexia_scores if score > 0.5)

    # Overall assessment
    print(f"\n🎯 SENTENCE ANALYSIS RESULTS:")
    print(f"   Image: {os.path.basename(image_path)}")
    print(f"   Total Letters: {len(letter_images)}")
    print(f"   Letters with Dyslexia Characteristics: {dyslexia_letters}/{len(letter_images)}")
    print(f"   Average Dyslexia Probability: {avg_dyslexia_prob:.4f} ({avg_dyslexia_prob*100:.1f}%)")
    print(f"   Highest Dyslexia Probability: {max_dyslexia_prob:.4f} ({max_dyslexia_prob*100:.1f}%)")
    print(f"   Lowest Dyslexia Probability: {min_dyslexia_prob:.4f} ({min_dyslexia_prob*100:.1f}%)")

    # Overall interpretation
    print(f"\n📊 OVERALL INTERPRETATION:")
    if avg_dyslexia_prob > 0.6:
        print(f"   🔴 HIGH: Strong indication of dyslexia characteristics across the sentence")
        overall_assessment = "High dyslexia likelihood"
    elif avg_dyslexia_prob > 0.4:
        print(f"   🟡 MODERATE: Some dyslexia characteristics detected in the sentence")
        overall_assessment = "Moderate dyslexia likelihood"
    elif avg_dyslexia_prob > 0.2:
        print(f"   🟡 LOW: Mild dyslexia characteristics in some letters")
        overall_assessment = "Low dyslexia likelihood"
    else:
        print(f"   🟢 NORMAL: Handwriting appears mostly normal")
        overall_assessment = "Normal handwriting"

    # Visualize results
    visualize_sentence_analysis(img_rgb, letter_results, bounding_boxes, image_path, overall_assessment)

    return {
        'type': 'sentence_analysis',
        'total_letters': len(letter_images),
        'dyslexia_letters': dyslexia_letters,
        'average_dyslexia_probability': avg_dyslexia_prob,
        'max_dyslexia_probability': max_dyslexia_prob,
        'min_dyslexia_probability': min_dyslexia_prob,
        'overall_assessment': overall_assessment,
        'letter_results': letter_results,
        'original_image': img_rgb
    }

def visualize_sentence_analysis(img_rgb, letter_results, bounding_boxes, image_path, overall_assessment):
    """
    Visualize the sentence analysis results
    """
    # Create figure with subplots
    fig = plt.figure(figsize=(20, 12))

    # Original image with bounding boxes
    plt.subplot(3, 1, 1)
    img_with_boxes = img_rgb.copy()

    # Draw bounding boxes and labels
    for i, (result, bbox) in enumerate(zip(letter_results, bounding_boxes)):
        x, y, w, h = bbox
        dyslexia_prob = result['dyslexia_probability']

        # Color based on dyslexia probability
        if dyslexia_prob > 0.5:
            color = (255, 0, 0)  # Red for dyslexia
            thickness = 3
        else:
            color = (0, 255, 0)  # Green for normal
            thickness = 2

        # Draw rectangle
        cv2.rectangle(img_with_boxes, (x, y), (x + w, y + h), color, thickness)

        # Add label
        label = f"{i+1}: {dyslexia_prob:.2f}"
        cv2.putText(img_with_boxes, label, (x, y-10), cv2.FONT_HERSHEY_SIMPLEX, 0.6, color, 2)

    plt.imshow(img_with_boxes)
    plt.title(f"Sentence Analysis: {os.path.basename(image_path)}\nOverall Assessment: {overall_assessment}",
              fontsize=14, fontweight='bold')
    plt.axis('off')

    # Individual letter analysis
    plt.subplot(3, 1, 2)
    letter_indices = [r['letter_index'] for r in letter_results]
    dyslexia_probs = [r['dyslexia_probability'] for r in letter_results]

    colors = ['red' if prob > 0.5 else 'green' for prob in dyslexia_probs]
    bars = plt.bar(letter_indices, dyslexia_probs, color=colors, alpha=0.7)

    plt.title('Dyslexia Probability by Letter', fontsize=12, fontweight='bold')
    plt.xlabel('Letter Index')
    plt.ylabel('Dyslexia Probability')
    plt.ylim(0, 1)
    plt.axhline(y=0.5, color='orange', linestyle='--', alpha=0.7, label='Threshold (0.5)')
    plt.legend()

    # Add value labels on bars
    for bar, prob in zip(bars, dyslexia_probs):
        plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                f'{prob:.2f}', ha='center', va='bottom', fontsize=8)

    # Show individual letters
    num_letters = len(letter_results)
    cols = min(10, num_letters)
    rows = (num_letters + cols - 1) // cols

    for i, result in enumerate(letter_results):
        plt.subplot(3, cols, 2*cols + i + 1)
        plt.imshow(result['letter_image'])

        # Color title based on prediction
        title_color = 'red' if result['dyslexia_probability'] > 0.5 else 'green'
        plt.title(f"L{result['letter_index']}\n{result['prediction'][:3]}\n{result['dyslexia_probability']:.2f}",
                 color=title_color, fontsize=8, fontweight='bold')
        plt.axis('off')

        if i >= cols - 1:  # Show only first row of letters to save space
            break

    plt.tight_layout()
    plt.show()

    # Summary statistics
    plt.figure(figsize=(12, 6))

    # Pie chart of dyslexia vs normal letters
    plt.subplot(1, 2, 1)
    dyslexia_count = sum(1 for r in letter_results if r['dyslexia_probability'] > 0.5)
    normal_count = len(letter_results) - dyslexia_count

    labels = ['Dyslexia Characteristics', 'Normal']
    sizes = [dyslexia_count, normal_count]
    colors = ['red', 'green']

    plt.pie(sizes, labels=labels, colors=colors, autopct='%1.1f%%', startangle=90)
    plt.title('Letter Classification Distribution', fontweight='bold')

    # Statistics
    plt.subplot(1, 2, 2)
    stats = [
        np.mean(dyslexia_probs),
        np.max(dyslexia_probs),
        np.min(dyslexia_probs),
        np.std(dyslexia_probs)
    ]
    stat_labels = ['Average', 'Maximum', 'Minimum', 'Std Dev']

    bars = plt.bar(stat_labels, stats, color=['blue', 'red', 'green', 'orange'], alpha=0.7)
    plt.title('Dyslexia Probability Statistics', fontweight='bold')
    plt.ylabel('Probability')
    plt.ylim(0, 1)

    # Add value labels
    for bar, stat in zip(bars, stats):
        plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                f'{stat:.3f}', ha='center', va='bottom', fontweight='bold')

    plt.tight_layout()
    plt.show()

def main():
    """Main function to test the image"""
    print("🧠 Dyslexia Detection Test - Single Letter or Sentence Analysis")
    print("=" * 70)

    # Test the provided image
    image_path = "23.jpg"  # The image you want to test

    if not os.path.exists(image_path):
        print(f"❌ Image not found: {image_path}")
        print("Please make sure '122.jpg' is in the current directory.")
        return

    # Ask user for analysis mode
    print(f"\n📋 ANALYSIS OPTIONS:")
    print(f"   1. Single Letter/Word Analysis (default)")
    print(f"   2. Sentence Analysis (segments into individual letters)")

    try:
        choice = input(f"\nEnter your choice (1 or 2, press Enter for 1): ").strip()
        if choice == "2":
            analyze_letters = True
            print(f"🔍 Selected: Sentence Analysis Mode")
        else:
            analyze_letters = False
            print(f"📝 Selected: Single Letter/Word Analysis Mode")
    except:
        analyze_letters = False
        print(f"📝 Using default: Single Letter/Word Analysis Mode")

    print(f"\n🖼️ Testing image: {image_path}")

    # Test the image
    result = test_image_for_dyslexia(image_path, analyze_letters=analyze_letters)

    if result:
        print(f"\n✅ Testing completed successfully!")

        # Display summary based on analysis type
        if result.get('type') == 'sentence_analysis':
            print(f"\n📊 SENTENCE ANALYSIS SUMMARY:")
            print(f"   Total Letters Analyzed: {result['total_letters']}")
            print(f"   Letters with Dyslexia Characteristics: {result['dyslexia_letters']}")
            print(f"   Average Dyslexia Probability: {result['average_dyslexia_probability']:.1%}")
            print(f"   Overall Assessment: {result['overall_assessment']}")
        else:
            print(f"\n📊 SINGLE IMAGE ANALYSIS SUMMARY:")
            print(f"   Prediction: {result['prediction']}")
            print(f"   Dyslexia Probability: {result['dyslexia_probability']:.1%}")
            print(f"   Confidence: {result['confidence']:.1%}")

        print(f"\n⚠️ IMPORTANT NOTES:")
        print(f"   - This is a research tool, not a medical diagnostic device")
        print(f"   - Results should be interpreted by qualified professionals")
        print(f"   - The model is trained on specific handwriting samples")
        if not analyze_letters:
            print(f"   - Single letter analysis may be less reliable than sentence analysis")
        else:
            print(f"   - Sentence analysis provides more comprehensive assessment")
            print(f"   - Individual letter variations are normal in handwriting")
    else:
        print(f"\n❌ Testing failed. Please check the model files and image.")

def test_with_sentence_mode():
    """Quick function to test in sentence mode"""
    image_path = "image.png"
    if os.path.exists(image_path):
        print("🧠 Testing in Sentence Analysis Mode...")
        result = test_image_for_dyslexia(image_path, analyze_letters=True)
        return result
    else:
        print(f"❌ Image not found: {image_path}")
        return None

def test_with_single_mode():
    """Quick function to test in single letter mode"""
    image_path = "image.png"
    if os.path.exists(image_path):
        print("🧠 Testing in Single Letter Mode...")
        result = test_image_for_dyslexia(image_path, analyze_letters=False)
        return result
    else:
        print(f"❌ Image not found: {image_path}")
        return None

if __name__ == "__main__":
    main()
